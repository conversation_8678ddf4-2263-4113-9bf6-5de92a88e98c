{
    "name" : "生意港",
    "appid" : "__UNI__7AC4BA7",
    "description" : "",
    "versionName" : "3.0.0",
    "versionCode" : 300,
    "transformPx" : true,
    "sassImplementationName" : "node-sass",
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "safearea" : {
            //安全区域配置，仅iOS平台生效    
            "background" : "#fff", //安全区域外的背景颜色，默认值为"#FFFFFF"    
            "bottom" : {
                // 底部安全区域配置    
                "offset" : "none" // 底部安全区域偏移，"none"表示不空出安全区域，"auto"自动计算空出安全区域，默认值为"none"    
            }
        },
        "screenOrientation" : [
            //可选，字符串数组类型，应用支持的横竖屏
            "portrait-primary", //可选，字符串类型，支持竖屏
            "portrait-secondary", //可选，字符串类型，支持反向竖屏
            "landscape-primary", //可选，字符串类型，支持横屏
            "landscape-secondary" //可选，字符串类型，支持反向横屏
        ],
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Camera" : {},
            "FaceID" : {},
            "LivePusher" : {},
            "VideoPlayer" : {},
            "Barcode" : {},
            "Geolocation" : {},
            "Maps" : {},
            "Share" : {},
            "Push" : {},
            "Record" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "schemes" : [ "borancs" ],
                "package" : "bscs.boran.com",
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_CONFIGURATION\"/>"
                ],
                "minSdkVersion" : 21,
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "targetSdkVersion" : 33
            },
            /* ios打包配置 */
            "ios" : {
                "urlschemewhitelist" : [ "borancs" ],
                "urltypes" : [
                    {
                        "urlschemes" : [ "borancs" ],
                        "id" : "bxcs.boran.com",
                        "description" : "勃冉测试 URL Scheme"
                    }
                ],
                "privacyDescription" : {
                    "NSCameraUsageDescription" : "\"为了让您能够拍摄照片并上传，我们需要使用您的摄像头。您可以用它来拍摄新的照片或扫描二维码。\"",
                    "NSPhotoLibraryUsageDescription" : "\"为了让您能够选择和上传照片，我们需要访问您的相册。您可以选择合适的图片来完善个人资料或分享内容。\"",
                    "NSPhotoLibraryAddUsageDescription" : "\"为了能够将图片保存到您的相册中，我们需要获取相册的写入权限。这样您就能保存喜欢的图片内容了。\"",
                    "NSMicrophoneUsageDescription" : "\"为了让您能够录制语音消息或视频内容，我们需要访问您的麦克风。这将用于语音通话和视频录制功能。\"",
                    "NSLocationWhenInUseUsageDescription" : "\"为了提供基于位置的服务和附近的信息，我们需要在您使用App期间访问位置信息。这将帮助您获得更好的本地化服务体验。\"",
                    "NSLocationAlwaysUsageDescription" : "\"为了持续为您提供位置相关服务，如实时导航、位置提醒等功能，我们需要在后台访问您的位置信息。您可以随时在设置中修改此权限。\"",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "\"为了给您提供更好的位置服务体验，我们需要访问您的位置信息。您可以选择'始终允许'来获取完整的位置服务，包括导航、位置提醒等功能，或选择'使用App时允许'来仅在使用时获取位置信息。\"",
                    "NSContactsUsageDescription" : "\"为了帮助您找到并连接认识的朋友，我们需要访问您的通讯录。这将用于查找已加入的联系人，让社交体验更加便捷。\"",
                    "NSCalendarsUsageDescription" : "\"为了帮助您更好地管理日程安排，我们需要访问您的日历。这样可以为您创建和管理活动提醒，同步重要日程。\""
                },
                "dSYMs" : false,
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:bxcs.boxuehao.cn" ]
                    }
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "maps" : {
                    "amap" : {
                        "name" : "amapAUT3caW2g",
                        "appkey_ios" : "e6bb8e33fba7102c915caf10a3f531de",
                        "appkey_android" : "a782f57ff5c96577edf32602499bc72e"
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wx2e830648fbe68582",
                        "UniversalLinks" : "https://bxcs.boxuehao.cn/bxcs/apple-app-site-association.json"
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "amap" : {
                        "name" : "amapAUT3caW2g",
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "e6bb8e33fba7102c915caf10a3f531de",
                        "appkey_android" : "a782f57ff5c96577edf32602499bc72e"
                    }
                },
                "push" : {}
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "iosStyle" : "common",
                "useOriginalMsgbox" : true
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "nativePlugins" : {}
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "permission" : {}
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "2",
    "h5" : {
        "devServer" : {
            "https" : false, // 是否启用 https 协议，默认false
            "port" : 8080,
            "disableHostCheck" : true,
            "proxy" : {
                // "/api" : {
                //     "target" : "http://************:48080",
                //     "changeOrigin" : true, //是否跨域
                //     "secure" : false, // 设置支持https协议的代理
                //     "pathRewrite" : {
                //         "^/api" : ""
                //     }
                // },
                "/cozeapi" : {
                    "target" : "https://api.coze.cn",
                    "changeOrigin" : true, //是否跨域
                    "secure" : true, // 设置支持https协议的代理
                    "pathRewrite" : {
                        "^/cozeapi" : ""
                    }
                },
                "/sygapi" : {
                    "target" : "http://***************:6808",
                    "changeOrigin" : true, //是否跨域
                    "secure" : true, // 设置支持https协议的代理
                    "pathRewrite" : {
                        "^/sygapi" : ""
                    }
                }
            }
        },
        "router" : {
            "mode" : "history",
            "base" : "/sygh5/"
        },
        "template" : "",
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "KA4BZ-LTA6I-AFNGE-52LUW-BC4AV-6OFVL"
                },
                "amap" : {
                    "key" : "d7cba4c5a05d83b5e4fba05d7f9ffc2c",
                    "securityJsCode" : "",
                    "serviceHost" : ""
                }
            }
        },
        // "publicPath" : "https://cdn.samsmagazine.huanxingweb.com",
        "title" : "勃学超市"
    },
    "_spaceID" : "mp-ed17ebb7-c3af-4a24-b16d-b77b773cacf8"
}
/* ios打包配置 *//* SDK配置 */

