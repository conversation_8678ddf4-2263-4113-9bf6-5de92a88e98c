<template>
	<view class="sygindex-container">
		<z-paging :show-scrollbar="false" refresher-only  @onRefresh="onRefresh" refresher-background='#fdfcf8' ref="paging" :use-page-scroll='true' :bg-color="'#fdfcf8'">
			<view slot="top">
				<my-nav :title='navtitle' :pageScrollTop='pageScrollTop'></my-nav>
			</view>
			<template #refresher="{refresherStatus}">
				<custom-refresher color="#000" :status="refresherStatus" />
			</template>
			<template #loadingMoreNoMore>
				<custom-nomore />
			</template>

			<!-- 顶部间距，避免被导航栏遮挡 -->
			<view class="top-spacer"></view>

			<!-- 顶部轮播图 -->
			<view class="banner-container" v-if="bannerList && bannerList.length > 0">
				<swiper circular :indicator-dots="true" :autoplay="true" interval="3000" duration="500" class="banner-swiper">
					<swiper-item v-for="(item, index) in bannerList" :key="index" class="banner-item">
						<image :src="item.imageUrl" mode="aspectFill" class="banner-image" @click="handleBannerClick(item)" @error="handleImageError"></image>
					</swiper-item>
				</swiper>
			</view>

			<!-- 轮播图加载状态 -->
			<view class="banner-loading" v-else>
				<text>轮播图加载中...</text>
			</view>

			<!-- 主要内容区域 -->
			<view class="main-content">
				<!-- 全流程创业服务标题 -->
				<view class="section-header">
					<view class="title-line"></view>
					<text class="title-text">全流程创业服务</text>
				</view>

				<!-- 服务网格 -->
				<view class="service-grid">
					<view class="service-item" v-for="(item, index) in serviceList" :key="index" @click="navigateToService(item)">
						<image :src="item.icon" mode="aspectFit" class="service-icon"></image>
						<view class="service-info">
							<text class="service-name">{{ item.name }}</text>
							<text class="service-desc">{{ item.desc }}</text>
						</view>
					</view>
				</view>

				<!-- AI工具标题 -->
				<view class="section-header">
					<view class="title-line"></view>
					<text class="title-text">AI工具</text>
				</view>

				<!-- AI工具网格 -->
				<view class="ai-grid">
					<view class="ai-item" v-for="(item, index) in aiToolList" :key="index" @click="navigateToAI(item)">
						<view class="ai-content" :style="{background: `linear-gradient(135deg, ${item.bgColor} 0%, ${item.bgColor}33 100%)`}">
							<view class="ai-text">
								<text class="ai-name">{{ item.name }}</text>
								<text class="ai-desc">{{ item.desc }}</text>
							</view>
							<image :src="item.icon" mode="aspectFill" class="ai-icon"></image>
						</view>
					</view>
				</view>

				<!-- 解你难处标题 -->
				<view class="section-header">
					<view class="title-line"></view>
					<text class="title-text">解你难处</text>
				</view>

				<!-- 解决方案列表 -->
				<view class="solution-list">
					<view class="solution-item" v-for="(item, index) in solutionList" :key="index" @click="navigateToSolution(item)">
						<image :src="item.icon" mode="aspectFit" class="solution-icon"></image>
						<view class="solution-content">
							<text class="solution-name">{{ item.name }}</text>
							<text class="solution-desc">{{ item.desc }}</text>
						</view>
						<view class="solution-arrow">
							<u-icon name="arrow-right" color="#999" size="16"></u-icon>
						</view>
					</view>
				</view>
			</view>
			<!-- 悬浮按钮 -->
			<view class="float-buttons">
				<!-- 删除了直播和招商会相关按钮 -->
				<view class="float-btn digital-teacher" @click="openDigitalTeacher" v-if="showDigitalTeacher">
					<image src="/static/icon_liveHint_ai.gif" mode="widthFix" class="float-icon" ></image>
					<!-- <text class="float-text">数字人讲解</text> -->
				</view>
			</view>
			
			<u-toast ref="uToast"></u-toast>
			<u-loading-page loading-text="马上就来" fontSize='14' icon-size="30" loading-mode="semicircle"
				:loading="loading"></u-loading-page>

			<!-- 直播未开始弹窗 -->
			<u-popup :show="showLiveNotStartedPopup" @close="closeLiveNotStartedPopup" mode="center"
				:closeable="true" closeIconPos="top-right" round="16" :safeAreaInsetBottom='false'>
				<view class="live-not-started-popup">
					<view class="live-popup-header">
						<text class="live-popup-title">{{liveNotStartedInfo.toptitle}}</text>
					</view>
					<view class="live-popup-content">
						<image
							:src="liveNotStartedInfo.coverImg"
							mode="widthFix"
							class="live-popup-cover"
						></image>
						<view class="live-popup-info">
							<view class="live-info-title">{{liveNotStartedInfo.title}}</view>
							<view class="live-info-meta">
								<u-icon name="account-fill" color="#666" size="20"></u-icon>
								<text class="live-info-author">{{liveNotStartedInfo.author}}</text>
							</view>
							<view class="live-info-meta">
								<u-icon name="clock-fill" color="#666" size="20"></u-icon>
								<text class="live-info-time">{{liveNotStartedInfo.startTime}}</text>
							</view>
						</view>
					</view>
					<view class="live-popup-status">

						<view class="live-status-text" v-if="liveNotStartedInfo.toptitle=='直播已结束'">直播已结束，敬请期待</view>
						<view class="live-status-text" v-else>直播尚未开始，请稍后再来</view>
					</view>
					<view class="live-popup-footer">
						<view class="live-popup-btn" @click="closeLiveNotStartedPopup">
							我知道了
						</view>
					</view>
				</view>
			</u-popup>
			
			<!-- 功能未开通弹窗 -->
			<u-popup :show="showContactUsPopup" @close="closeContactUsPopup" mode="center"
				:closeable="true" closeIconPos="top-right" round="16" :safeAreaInsetBottom='false'>
				<view class="contact-us-popup">
					<view class="contact-popup-header">
						<text class="contact-popup-title">联系我们开通</text>
					</view>
					<view class="contact-popup-content">
						<!-- <image
							src="/static/syg/contact_us.png"
							mode="widthFix"
							class="contact-popup-image"
						></image> -->
						<view class="contact-popup-info">
							<view class="contact-info-title">该功能需联系客服开通</view>
							<view class="contact-info-desc">目前仅"算盈利"功能可试用，其他功能需联系我们开通权限</view>
							<view class="contact-info-meta">
								<u-icon name="phone-fill" color="#666" size="20"></u-icon>
								<text class="contact-info-phone">400-0099-771</text>
							</view>
							<view class="contact-info-meta">
								<u-icon name="weixin-fill" color="#666" size="20"></u-icon>
								<text class="contact-info-wechat">15253882222</text>
							</view>
						</view>
					</view>
					<view class="contact-popup-footer">
						<view class="contact-popup-btn" @click="copyWechat">
							复制微信号
						</view>
						<view class="contact-popup-btn contact-popup-btn-call" @click="makePhoneCall">
							立即拨打
						</view>
					</view>
				</view>
			</u-popup>

			<!-- 底部组件 -->
			<my-bottom></my-bottom>
		</z-paging>
	</view>
</template>

<script>
	import myBottom from '@/components/my-bottom/my-bottom.vue'

	export default {
		components: {
			myBottom
		},
		data() {
			return {
				loading: true,
				pageScrollTop: 0, // 页面滚动距离
				bgColor: 'rgba(255,255,255)',
				navtitle: '生意港',
				bannerList: [],
				iconList: [],
				isPlaying: false,
				allMenuItems: [],
				moduleData: {},
				titleBgImage: '',
				titleX: 0,
				titleY: 0,
				titleFontSize: 0,
				titleColor: '',
				titleFontWeight: '',
				titleBarHeight: 0,
				showGallery: false,
				galleryTitle: '',
				galleryIndex: 0,
				galleryImages: [],
				currentVideoRecord: null, // 当前播放视频的记录
				companyId: '', // 公司ID
				lastScrollTop: 0, // 新增的lastScrollTop属性
				newsPage: 1, // 新闻当前页码
				newsLoading: false, // 新闻加载状态
				newsHasMore: true, // 是否还有更多新闻

				// 控制悬浮按钮显示
				showDigitalTeacher: false, // 数字人讲解按钮
				
				// 暂未开播弹窗控制
				showLiveNotStartedPopup: false,
				liveNotStartedInfo: {
					toptitle:'',
					title: '',
					coverImg: '',
					startTime: '',
					author: ''
				},
				
				// 联系我们开通弹窗
				showContactUsPopup: false,

				// 新增数据结构
				serviceList: [
					{
						id: 1,
						name: '看方向',
						desc: '选择创业赛道',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						menuCode: 'direction'
					},
					{
						id: 2,
						name: '算盈利',
						desc: '盈利计算器',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						menuCode: 'profit'
					},
					{
						id: 3,
						name: '找供应',
						desc: '产品上下游',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						menuCode: 'location'
					},
					{
						id: 4,
						name: '引流量',
						desc: 'AI生成视频',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						menuCode: 'traffic'
					},
					{
						id: 5,
						name: '管营销',
						desc: '矩阵营销管理',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						menuCode: 'marketing'
					},
					{
						id: 6,
						name: '学课程',
						desc: '行业导师陪您',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						menuCode: 'course'
					}
				],
				aiToolList: [
					{
						id: 1,
						name: 'AI助理',
						desc: '智能助理服务',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						bgColor: '#4A90E2',
						menuCode: 'ai_assistant'
					},
					{
						id: 2,
						name: '海报生成',
						desc: '轻松制作海报',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						bgColor: '#F5A623',
						menuCode: 'ai_report'
					},
					{
						id: 3,
						name: 'AI配音',
						desc: '音色丰富 情感饱满',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						bgColor: '#50C8C8',
						menuCode: 'ai_voice'
					},
					{
						id: 4,
						name: '数字人',
						desc: '个性化 私人定制',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						bgColor: '#9013FE',
						menuCode: 'ai_digital'
					}
				],
				solutionList: [
					{
						id: 1,
						name: '候选位置评估',
						desc: '已有候选点位，位置好不好一测便知',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						menuCode: 'store_evaluation'
					},
					{
						id: 2,
						name: '开店位置选择',
						desc: '暂无备选点位，大数据智能分析，选择热门点位',
						icon: '/static/syg/index/<EMAIL>', // 预留图片路径
						menuCode: 'location_evaluation'
					}
				]
			}
		},
		onLoad() {
			// 初始化公司ID
			// this.initCompanyId();

			setTimeout(() => this.loading = false, 1000)
			// // 获取菜单数据
			// this.getMenu()

			// 获取顶部轮播图
			this.getAdvertisingData()

			// 删除了直播相关API调用
		},
		onPageScroll(e) {
			this.pageScrollTop = Math.floor(e.scrollTop);
		},
		onUnload() {
			// 清理定时器已删除
		},
		methods: {
			// 初始化公司ID
			initCompanyId() {
				// 默认公司ID
				// const defaultCompanyId = 'a3f2de53b88e4c5ab7c4af3442d37400';//科学队长
				// const defaultCompanyId = 'f78d05e497db433c8a96f44b548fce47';//勃学超市
				
				// 1. 检查是否存在全局公司ID
				const app = getApp();
				if (app.globalData && app.globalData.companyId) {
					this.companyId = app.globalData.companyId;
				} else {
					// 2. 如果没有全局ID，则设置默认ID并存储到全局
					this.companyId = '';
					
					// 3. 如果全局数据对象不存在，初始化它
					if (!app.globalData) {
						app.globalData = {};
					}
					
					// 4. 设置全局公司ID
					app.globalData.companyId = this.companyId;
					uni.setStorageSync('companyId',this.companyId)
				}
				
				console.log('当前使用的公司ID:', this.companyId);
			},
			// 获取顶部广告轮播图
			getAdvertisingData() {
				console.log('开始获取轮播图数据...');
				this.syghttp.ajax({
					url: this.syghttp.api.getAdvertising,
					method: 'POST',
					data: {
						"layoutPosition": "APP_HomeTop",
						"advertisingSize": 10
					},
					success: (res) => {
						console.log('轮播图数据返回:', res);
						if (res.code == 1000) {
							if (res.data && res.data.advertisings && Array.isArray(res.data.advertisings)) {
								console.log('轮播图数据格式正确，开始处理...');
								this.bannerList = res.data.advertisings.map(item => {
									return {
										id: item.id || '',
										imageUrl: item.fileUrl || '',
										linkUrl: item.dataId || '',
										title: item.tittle || ''
									};
								});
								console.log('处理后的轮播图数据:', this.bannerList);
								console.log('轮播图数据长度:', this.bannerList.length);

								// 强制更新视图
								this.$forceUpdate();
							} else {
								console.log('轮播图数据格式不正确:', res.data);
							}
						} else {
							console.log('轮播图API返回错误:', res.msg);
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						console.error('获取轮播图数据失败:', err);
						uni.showToast({
							title: '获取轮播图数据失败',
							icon: 'none'
						});
					}
				});
			},

			handleBannerClick(item) {
				if (item.linkUrl) {
					// 这里可以根据linkUrl进行相应的跳转处理
					console.log('点击轮播图:', item);
				}
			},

			// 图片加载错误处理
			handleImageError(e) {
				console.error('轮播图图片加载失败:', e);
				console.log('当前轮播图数据:', this.bannerList);
			},

			// 导航到服务页面
			navigateToService(item) {
				console.log('点击服务:', item);
				// 只有"算盈利"功能可用，其他功能显示联系开通弹窗
				if (item.menuCode === 'profit') {
					// 导航到算盈利页面
					uni.navigateTo({
						url: `/pages/service/${item.menuCode}`
					});
				} else {
					// 显示联系我们开通弹窗
					this.showContactUs();
				}
			},

			// 导航到AI工具页面
			navigateToAI(item) {
				console.log('点击AI工具:', item);
				// 显示联系我们开通弹窗
				this.showContactUs();
			},

			// 导航到解决方案页面
			navigateToSolution(item) {
				console.log('点击解决方案:', item);
				// 显示联系我们开通弹窗
				this.showContactUs();
			},
			navigateTo(menuCode) {
				// 使用 uni.createSelectorQuery() 替代 document.getElementById
				const query = uni.createSelectorQuery().in(this);
				query.select(`#module-${menuCode}`).boundingClientRect(data => {
					if (data) {
						// 平滑滚动到该位置
						uni.pageScrollTo({
							scrollTop: data.top - 100, // 减去一些距离，确保标题在视图中
							duration: 300
						});
					} else {
						console.log('未找到对应模块:', menuCode);
					}
				}).exec();
			},
			playMainVideo() {
				this.isPlaying = true
			},
			openDigitalTeacher() {
				uni.navigateTo({
					url: '/pages/investment/aiList'
				})
			},
			openBusinessMeeting() {
				uni.navigateTo({
					url: '/pages/business/meeting'
				})
			},
			getMenu() {
				this.syghttp.ajax({
					url: this.syghttp.api.homeMenu,
					method: 'GET',
					data:{
						companyId: this.companyId,
					},
					success: (res) => {
						if (res.code === 1000) {
							// 处理菜单数据
							if (res.data && res.data.dtos && res.data.dtos.length > 0) {
								// 调试信息
								console.log('所有菜单项:', res.data.dtos);
								
								// 处理所有菜单项
								this.allMenuItems = res.data.dtos;
								
								// 过滤金刚区菜单项
								this.iconList = res.data.dtos
									.filter(item => item.headColumn === 1)
									.map((item, index) => {
										return {
											id: index + 1,
											name: item.menuName,
											icon: item.menuIcon || '/static/image/default-icon.png',
											menuCode: item.menuCode,
											englishName: item.englishName
										}
									});
								
								// 获取所有模块的数据
								this.fetchAllModuleData();
								
								// 获取标题栏配置
								this.getStartupImgConfig();
								// this.getIconList();
							}
						}
					}
				})
			},
			getIconList() {
				this.syghttp.ajax({
					url: this.syghttp.api.apiGetPostInformation,
					method: 'POST',
					data:{
						"companyId": this.companyId,
						"page": {
						    pageNo:1,
						    pageSize: 2,
							}
					},
					success: (res) => {
						if (res.code === 1000) {
							// 处理菜单数据
							if (res.data && res.data.dtos && res.data.dtos.length > 0) {
								// 处理所有菜单项
								this.allMenuItems = res.data.dtos;
								
								// 过滤金刚区菜单项
								this.iconList = res.data.dtos
									.filter(item => item.headColumn === 1)
									.map((item, index) => {
										return {
											id: index + 1,
											name: item.menuName,
											icon: item.menuIcon || '/static/image/default-icon.png',
											menuCode: item.menuCode,
											englishName: item.englishName
										}
									});
								
								// 获取所有模块的数据
								this.fetchAllModuleData();
								
								// 获取标题栏配置
								this.getStartupImgConfig();
							}
						}
					}
				})
			},
			// 获取所有模块数据
			fetchAllModuleData() {
				if (!this.allMenuItems || this.allMenuItems.length === 0) return;
				
				// 创建模块数据的对象
				this.moduleData = {};
				
				// 遍历所有菜单项，获取每个模块的数据
				this.allMenuItems.forEach(menu => {
					this.getModuleData(menu.menuCode);
				});
			},
			
			// 获取单个模块的数据
			getModuleData(menuCode) {
				// 如果是新闻模块，使用专门的新闻接口
				if (menuCode === 'new') {
					console.log('正在使用apiGetPostInformation接口加载新闻数据');
					this.newsLoading = true;
					this.newsPage = 1; // 重置页码
					this.syghttp.ajax({
						url: this.syghttp.api.apiGetPostInformation,
						method: 'POST',
						data: {
							"companyId": this.companyId,
							"page": {
								"pageNo": this.newsPage,
								"pageSize": 10
							}
						},
						success: (res) => {
							if (res.code === 1000) {
								console.log('新闻接口数据返回:', res.data);
								
								// 存储模块数据
								if (res.data && res.data.items && res.data.items.items) {
									this.moduleData[menuCode] = res.data.items.items;
									console.log('新闻数据已存储，长度:', this.moduleData[menuCode].length);
									
									// 检查是否还有更多数据
									if (res.data.items.totalCount > this.moduleData[menuCode].length) {
										this.newsHasMore = true;
									} else {
										this.newsHasMore = false;
									}
									
									// 更新视图
									this.$forceUpdate();
								}
							}
							this.newsLoading = false;
						}
					});
					return; // 不再执行后续的默认接口调用
				}
				
				// 其他模块使用默认接口
				this.syghttp.ajax({
					url: this.syghttp.api.indexSwiper,
					method: 'POST',
					data: {
						"code": menuCode,
						"keyword": "",
						"page": {
							"maxResultCount": 0,
							"pageNo": 1,
							"pageSize": 40,
							"skipCount": 0
						},
						"companyId": this.companyId,
					},
					success: (res) => {
						if (res.code === 1000) {
							// 存储模块数据
							if (res.data && res.data.items && res.data.items.items) {
								this.moduleData[menuCode] = res.data.items.items;
								
								// 如果是首页轮播图，特殊处理
								// if (menuCode === 'PromotionalVideo') {
								// 	console.log('PromotionalVideo模块覆盖轮播图数据:', res.data.items.items);
								// 	this.bannerList = res.data.items.items.map(item => {
								// 		return {
								// 			id: item.id || '',
								// 			imageUrl: item.cover || item.fileUrl || (item.fileDTOList && item.fileDTOList.length > 0 ? item.fileDTOList[0].fileUrl : ''),
								// 			linkUrl: item.url || '',
								// 			title: item.tittle || ''
								// 		}
								// 	});
								// 	console.log('PromotionalVideo处理后的轮播图数据:', this.bannerList);
								// }
								
								// 更新视图
								this.$forceUpdate();
							}
						}
					}
				});
			},
			
			// 获取标题栏配置
			getStartupImgConfig() {
				this.syghttp.ajax({
					url: this.syghttp.api.getStartupImgConfig,
					method: 'POST',
					data: {
						"pageSpliterBarLocation": 1,
						"companyId": this.companyId
					},
					success: (res) => {
						if (res.code === 1000) {
							if (res.data && res.data.item) {
								// 处理标题栏样式
								if (res.data.item.imageList && res.data.item.imageList.length > 0) {
									this.titleBgImage = res.data.item.imageList[0].fileUrl;
								}
								
								// 处理字体样式
								if (res.data.item.splitterFontStyle) {
									try {
										const fontStyle = JSON.parse(res.data.item.splitterFontStyle);
										this.titleX = fontStyle.x;
										console.log('字体位置:', fontStyle.x, fontStyle.y);
										this.titleY = fontStyle.y;
													this.titleFontSize = fontStyle.fontSize;
													console.log(fontStyle)
										this.titleColor = fontStyle.color;
										this.titleFontWeight = fontStyle.fontWeight;
										this.titleBarHeight = fontStyle.splitBarImgHeight;
										console.log(this.titleColor,this.titleFontSize,this.titleFontWeight,this.titleBarHeight)
									} catch (e) {
										console.error('解析字体位置失败', e);
									}
								}
								
								// // 处理其他样式
								// if (res.data.item.commentStyle) {
								// 	try {
								// 		const commentStyle = JSON.parse(res.data.item.commentStyle);
								// 		this.titleFontSize = commentStyle.fontSize;
								// 		this.titleColor = commentStyle.color;
								// 		this.titleFontWeight = commentStyle.fontWeight;
								// 		this.titleBarHeight = commentStyle.splitBarImgHeight;
								// 		console.log(commentStyle)
								// 	} catch (e) {
								// 		console.error('解析字体样式失败', e);
								// 	}
								// }
								
								// 更新视图
								this.$forceUpdate();
							}
						}
					}
				});
			},
			
			// 处理不同类型内容的点击
			handleItemClick(item) {
				if (!item) return;
				
				// 根据type类型判断跳转方式
				switch(item.type) {
					case 0: // 打开图片或新闻
						// 如果有content字段或者是来自新闻模块，说明是新闻
						if (item.content) {
							uni.navigateTo({
								url: `/pages/investment/news?id=${item.id}&title=${encodeURIComponent(item.tittle || '')}&companyId=${this.companyId}`
							});
						// } else if (item.cover) {
						// 	this.handleImagePreview(item.cover, [{fileUrl: item.cover}]);
						// } else if (item.fileDTOList && item.fileDTOList.length > 0) {
						}
						else{
							this.handleImagePreview(item.fileDTOList[0].fileUrl, item.fileDTOList);
						}
						break;
					case 1: // 视频查看
						uni.navigateTo({
							url: `/pages/investment/video?videoUrl=${encodeURIComponent(item.url || item.videoUrl)}&title=${encodeURIComponent(item.tittle || '')}&id=${item.id || ''}&digest=${encodeURIComponent(item.digest || '')}&creationTime=${encodeURIComponent(item.creationTime || '')}&cover=${encodeURIComponent(item.cover || '')}&companyId=${this.companyId}`
						});
						break;
					case 2: // webview720全景
						uni.navigateTo({
							url: `/pages/investment/webview?url=${encodeURIComponent(item.panorama)}&title=${encodeURIComponent(item.tittle || '')}`
						});
						break;
					case 3: // 富文本
						uni.navigateTo({
							url: `/pages/rich/text?id=${item.id}&title=${encodeURIComponent(item.tittle || '')}`
						});
						break;
					default:
						console.log('未知类型:', item.type);
				}
			},
			
			// 使用uni-app原生的图片预览功能
			handleImagePreview(current, images) {
				try {
					// 提取文件URL
					const urls = images.map(img => typeof img === 'string' ? img : img.fileUrl);
					// 使用uni-app原生的预览图片API
					uni.previewImage({
						current: current, // 当前显示图片的链接
						urls: urls, // 所有图片的链接列表
						indicator: 'number', // 显示数字索引
						loop: true, // 允许循环预览
						success: function(res) {
							console.log('图片预览成功');
						},
						fail: function(err) {
							console.error('图片预览失败', err);
						}
					});
				} catch (error) {
					console.error('预览图片出错', error);
					uni.showToast({
						title: '图片预览失败',
						icon: 'none'
					});
				}
			},
			playVideoItem(item) {
				if (!item || (!item.url && !item.videoUrl)) {
					uni.showToast({
						title: '视频链接不存在',
						icon: 'none'
					});
					return;
				}
				
				// 导航到视频播放页
				uni.navigateTo({
					url: `/pages/investment/video?videoUrl=${encodeURIComponent(item.url || item.videoUrl)}&title=${encodeURIComponent(item.tittle || '')}&id=${item.id || ''}&digest=${encodeURIComponent(item.digest || '')}&creationTime=${encodeURIComponent(item.creationTime || '')}&cover=${encodeURIComponent(item.cover || '')}&companyId=${this.companyId}`
				});
			},
			// 视频播放回调
			handleVideoPlay(videoItem) {
				// 记录开始播放时间
				this.recordVideoWatching(videoItem, true);
			},
			
			// 视频暂停回调
			handleVideoPause() {
				// 记录暂停时的观看时间
				if (this.currentVideoRecord) {
					this.updateVideoWatchingTime(false);
				}
			},
			
			// 视频结束回调
			handleVideoEnd() {
				// 视频播放结束，记录观看时间
				if (this.currentVideoRecord) {
					this.updateVideoWatchingTime(true);
				}
				this.isPlaying = false;
			},
			
			// 视频错误回调
			handleVideoError() {
				uni.showToast({
					title: '视频加载失败',
					icon: 'none'
				});
				this.isPlaying = false;
			},
			
			// 记录视频观看
			recordVideoWatching(videoItem, isStart = true) {
				if (!videoItem || !videoItem.id) return;
				
				// 如果是开始播放
				if (isStart) {
					// 创建新的观看记录
					this.currentVideoRecord = {
						videoId: videoItem.id,
						startTime: new Date().getTime(),
						duration: 0
					};
					
					// 调用 API 记录开始观看
					this.syghttp.ajax({
						url: this.syghttp.api.updateWatchAppHomePageTime || '/api/updateWatchAppHomePageTime',
						method: 'POST',
						data: {
							browseRecordId: videoItem.id,
							duration: 0,
							companyId: this.companyId
						},
						success: (res) => {
							console.log('记录视频开始观看成功', res);
						},
						fail: (err) => {
							console.error('记录视频开始观看失败', err);
						}
					});
				}
			},
			
			// 更新视频观看时间
			updateVideoWatchingTime(isComplete = false) {
				if (!this.currentVideoRecord) return;
				
				// 计算观看时长（秒）
				const endTime = new Date().getTime();
				const duration = Math.floor((endTime - this.currentVideoRecord.startTime) / 1000);
				
				// 更新当前记录
				this.currentVideoRecord.duration = duration;
				
				// 调用 API 更新观看时长
				this.syghttp.ajax({
					url: this.syghttp.api.updateWatchAppHomePageTime || '/api/updateWatchAppHomePageTime',
					method: 'POST',
					data: {
						browseRecordId: this.currentVideoRecord.videoId,
						duration: duration,
						companyId: this.companyId
					},
					success: (res) => {
						console.log('更新视频观看时长成功', res);
					},
					fail: (err) => {
						console.error('更新视频观看时长失败', err);
					}
				});
				
				// 如果视频播放完成，清空当前记录
				if (isComplete) {
					this.currentVideoRecord = null;
				}
			},
			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return '';
				
				// 尝试解析日期字符串
				try {
					const date = new Date(dateStr);
					const year = date.getFullYear();
					const month = String(date.getMonth() + 1).padStart(2, '0');
					const day = String(date.getDate()).padStart(2, '0');
					
					return `${year}-${month}-${day}`;
				} catch (e) {
					console.error('日期格式化错误:', e);
					return dateStr; // 如果出错则返回原始字符串
				}
			},
			loadMoreNews() {
				// 如果已经在加载或没有更多数据，不再请求
				if (this.newsLoading || !this.newsHasMore) return;
				
				this.newsPage++;
				this.newsLoading = true;
				this.syghttp.ajax({
					url: this.syghttp.api.apiGetPostInformation,
					method: 'POST',
					data: {
						"companyId": this.companyId,
						"page": {
							"pageNo": this.newsPage,
							"pageSize": 10
						}
					},
					success: (res) => {
						if (res.code === 1000) {
							console.log('加载更多新闻数据返回:', res.data);
							
							// 存储模块数据
							if (res.data && res.data.items && res.data.items.items) {
								if (res.data.items.items.length > 0) {
									this.moduleData['new'] = [...this.moduleData['new'], ...res.data.items.items];
									console.log('新闻数据已追加，当前长度:', this.moduleData['new'].length);
									
									// 检查是否还有更多数据
									if (res.data.items.totalCount > this.moduleData['new'].length) {
										this.newsHasMore = true;
									} else {
										this.newsHasMore = false;
									}
								} else {
									// 没有更多数据了
									this.newsHasMore = false;
								}
								
								// 更新视图
								this.$forceUpdate();
							}
						} else {
							this.newsHasMore = false;
						}
						this.newsLoading = false;
					},
					fail: (err) => {
						console.error('加载更多新闻失败:', err);
						this.newsLoading = false;
					}
				});
			},
			// 获取首页直播数据
			// 该方法已被删除
			
			// 获取直播视频页面数据
			// 该方法已被删除
			
			// 关闭直播未开始弹窗
			closeLiveNotStartedPopup() {
				this.showLiveNotStartedPopup = false;
			},
			// 下拉刷新方法
			async onRefresh() {
				console.log('执行下拉刷新');
				// 重置页面状态
				this.newsPage = 1;
				this.newsHasMore = true;
				
				try {
					// 重新初始化公司ID
					this.initCompanyId();
					
					// 获取菜单数据（会触发fetchAllModuleData）
					// await this.getMenu();
					
					// 获取顶部轮播图
					await this.getAdvertisingData();
					
					// 提示刷新成功
					// this.$refs.uToast.show({
					// 	type: 'success',
					// 	message: '刷新成功'
					// });
				} catch (error) {
					console.error('刷新失败:', error);
					this.$refs.uToast.show({
						type: 'error',
						message: '刷新失败，请重试'
					});
				} finally {
					// 完成下拉刷新
					this.$refs.paging.complete();
				}
			},
			// 显示联系我们弹窗
			showContactUs() {
				this.showContactUsPopup = true;
			},
			
			// 关闭联系我们弹窗
			closeContactUsPopup() {
				this.showContactUsPopup = false;
			},
			
			// 复制微信号
			copyWechat() {
				uni.navigateTo({
					url: '/pages/guide/guide'
				})
				uni.setClipboardData({
					data: '15253882222',
					success: () => {
						uni.showToast({
							title: '微信号已复制',
							icon: 'success'
						});
						setTimeout(() => {
							this.closeContactUsPopup();
						}, 1500);
					}
				});
			},
			
			// 拨打电话
			makePhoneCall() {
				uni.makePhoneCall({
					phoneNumber: '400-0099-771',
					success: () => {
						console.log('拨打电话成功');
						this.closeContactUsPopup();
					}
				});
			}
		}
	}
</script>

<style>
	page,
	body {
		background-color: #fdfcf8;
		height: 100vh;
	}
</style>

<style lang="scss" scoped>
	.sygindex-container {
		background-color: #fdfcf8;
		min-height: 100vh;
	}
	
	/* 顶部间距，避免被导航栏遮挡 */
	.top-spacer {
		height: calc(var(--status-bar-height) + 20upx);
		width: 100%;
	}

	/* 轮播图样式 */
	.banner-container {
		width: 100%;
		height: 360upx;
		padding: 20upx;
		box-sizing: border-box;
	}

	.banner-swiper {
		width: 100%;
		height: 100%;
		border-radius: 20upx;
		overflow: hidden;
		box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.1);
	}

	.banner-item {
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 20upx;
		overflow: hidden;
	}

	.banner-image {
		width: 100%;
		height: 100%;
		border-radius: 20upx;
	}

	/* 轮播图加载状态 */
	.banner-loading {
		width: 100%;
		height: 360upx;
		padding: 20upx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f5f5f5;
		border-radius: 20upx;
		// margin: 0 20upx;

		text {
			color: #999;
			font-size: 28upx;
		}
	}

	/* 主要内容区域 */
	.main-content {
		// background: linear-gradient(135deg, #E3F2FD 0%, #F8F9FA 100%);
		padding: 20upx 30upx 40upx;
		margin-top: 10upx;
	}

	/* 标题样式 */
	.section-header {
		display: flex;
		align-items: center;
		margin-bottom: 30upx;
		position: relative;
	}

	.title-line {
		width: 6upx;
		height: 32upx;
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		border-radius: 3upx;
		margin-right: 1upx;
	}

	.title-text {
		font-size: 32upx;
		font-weight: bold;
		color: #333;
		position: relative;
		z-index: 1;
	}
	
	.title-text:after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 10upx;
		background-color: rgba(255, 107, 53, 0.1);
		z-index: -1;
	}

	/* 服务网格样式 - 修改为每行3个 */
	.service-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-bottom: 40upx;
	}

	.service-item {
		width: 31%;
		border-radius: 16upx;
		padding: 20upx 15upx;
		margin-bottom: 20upx;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.08);
		transition: transform 0.3s ease;
	}
	
	/* 为每个服务项添加不同的渐变背景色 */
	.service-item:nth-child(1) {
		background: linear-gradient(135deg, #FFE8E0 0%, #FFF5F2 100%);
	}
	
	.service-item:nth-child(2) {
		background: linear-gradient(135deg, #E0ECFF 0%, #F2F6FF 100%);
	}
	
	.service-item:nth-child(3) {
		background: linear-gradient(135deg, #E0FFF1 0%, #F2FFF8 100%);
	}
	
	.service-item:nth-child(4) {
		background: linear-gradient(135deg, #FFE0F5 0%, #FFF2FA 100%);
	}
	
	.service-item:nth-child(5) {
		background: linear-gradient(135deg, #FFF0E0 0%, #FFF8F2 100%);
	}
	
	.service-item:nth-child(6) {
		background: linear-gradient(135deg, #E0F4FF 0%, #F2FAFF 100%);
	}

	.service-item:active {
		transform: scale(0.96);
	}

	.service-icon {
		width: 80upx;
		height: 80upx;
		margin-bottom: 15upx;
	}
	
	.service-info {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
	}

	.service-name {
		font-size: 28upx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8upx;
	}

	.service-desc {
		font-size: 22upx;
		color: #666;
		text-align: center;
	}

	/* AI工具网格样式 */
	.ai-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-bottom: 40upx;
	}

	.ai-item {
		width: 48%;
		margin-bottom: 20upx;
	}

	.ai-content {
		height:155upx;
		border-radius: 16upx;
		padding: 15upx 30upx;
		display: flex;
		align-items: center;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;
	}

	.ai-item:active .ai-content {
		transform: scale(0.96);
	}

	.ai-icon {
		width: 90upx;
		height: 130upx;
		margin-left: 15upx;
	}
	.ai-text {
		flex: 1;
	}

	.ai-name {
		font-size: 32upx;
		font-weight: bold;
		color: #fff;
		display: block;
		margin-bottom: 10upx;
	}

	.ai-desc {
		font-size: 20upx;
		color: rgba(255, 255, 255, 0.9);
		display: block;
	}

	/* 解决方案列表样式 */
	.solution-list {
		margin-bottom: 30upx;
	}

	.solution-item {
		background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%);
		border-radius: 16upx;
		padding: 25upx 20upx;
		margin-bottom: 20upx;
		display: flex;
		align-items: center;
		box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.08);
		transition: transform 0.3s ease;
	}

	.solution-item:active {
		transform: scale(0.96);
	}

	.solution-icon {
		width: 150upx;
		height: 150upx;
		margin-right: 20upx;
	}

	.solution-content {
		flex: 1;
	}

	.solution-name {
		font-size: 28upx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8upx;
	}

	.solution-desc {
		font-size: 24upx;
		color: #666;
		display: block;
	}

	.solution-arrow {
		margin-left: 15upx;
	}
	
	.section-title {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 30upx 0;
	}
	
	.title-icon {
		width: 50upx;
		height: 30upx;
	}
	
	.title-text {
		font-size: 32upx;
		font-weight: bold;
		
		margin: 0 20upx;
	}
	
	.intro-container {
		padding: 0 30upx;
	}
	
	.intro-item {
		width: 100%;
		border-radius: 10upx;
		overflow: hidden;
	}
	
	.intro-image {
		width: 100%;
		border-radius: 10upx;
	}
	
	/* 企业资质轮播图 */
	.cert-container {
		padding: 20upx 30upx;
		position: relative;
		// margin-top: 30upx;
	}
	
	.cert-title {
		font-size: 28upx;
		color: #333;
		margin-bottom: 20upx;
		text-align: center;
		font-weight: bold;
	}
	
	.cert-grid {
		display: flex;
		flex-wrap: wrap;
	}
	
	.cert-grid-item {
		width: 25%;
		padding: 5upx;
		position: relative;
		aspect-ratio: 1/1;
		height: 150upx;
	}
	
	.cert-grid-image {
		width: 100%;
		height: 100%;
		border-radius: 6upx;
		object-fit: cover;
		box-shadow: 0 2upx 6upx rgba(0, 0, 0, 0.1);
	}
	
	.cert-grid-mask {
		position: absolute;
		top: 5upx;
		left: 5upx;
		right: 5upx;
		bottom: 5upx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 6upx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.cert-grid-more {
		font-size: 32upx;
		font-weight: bold;
		color: #fff;
	}
	
	.teacher-swiper {
		width: 100%;
		height: 400upx;
		padding: 0 20upx;
	}
	
	.teacher-image {
		width: 100%;
		height: 100%;
		border-radius: 10upx;
	}
	
	.video-container {
		padding: 0 20upx;
	}
	
	.main-video-wrap {
		width: 100%;
		position: relative;
		border-radius: 10upx;
		overflow: hidden;
		background-color: #000;
		aspect-ratio: 16/9; /* 设置宽高比为16:9，对于横屏视频 */
	}
	
	.video-cover {
		width: 100%;
		height: 100%;
		object-fit: contain; /* 保证图片完整显示 */
		background-color: #000;
	}
	
	.play-icon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 1;
		width: 80upx;
		height: 80upx;
		background: rgba(0, 0, 0, 0.4);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding-left: 6upx; /* 微调图标位置 */
	}
	
	.main-video {
		width: 100%;
		height: 100%;
		background-color: #000;
	}
	
	.product-list {
		padding: 0 20upx;
	}
	
	.scroll-view {
		width: 100%;
		white-space: nowrap;
	}
	
	.product-scroll {
		display: flex;
	}
	
	.product-item {
		display: inline-block;
		width: 280upx;
		margin-right: 20upx;
	}
	
	.product-image {
		width: 280upx;
		height: 200upx;
		border-radius: 10upx;
	}
	
	.product-name {
		font-size: 28upx;
		color: #333;
		margin-top: 10upx;
		display: block;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	.form-container {
		background-color: #fff;
		border-radius: 10upx;
		padding: 30upx;
		margin: 20upx;
	}
	
	.form-item {
		margin-bottom: 30upx;
	}
	
	.input-field {
		width: 100%;
		height: 80upx;
		border-radius: 10upx;
		background-color: #f5f5f5;
		padding: 0 20upx;
		font-size: 28upx;
	}
	
	.submit-btn {
		border: 0;
		width: 100%;
		height: 80upx;
		background: #2BCBD4;
		border-radius: 40upx;
		color: #fff;
		font-size: 30upx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.news-list {
		padding: 0 20upx;
	}
	
	.news-item {
		background-color: #fff;
		border-radius: 10upx;
		padding: 20upx;
		margin-bottom: 20upx;
	}
	
	.news-title {
		font-size: 28upx;
		color: #333;
		line-height: 1.5;
	}
	
	.news-info {
		display: flex;
		justify-content: space-between;
		margin-top: 10upx;
	}
	
	.news-date,
	.news-views {
		font-size: 24upx;
		color: #999;
	}
	
	.float-buttons {
		position: fixed;
		right: 20upx;
		bottom: 150upx;
		z-index: 10; /* 降低层级，使其低于loading页面 */
		display: flex;
		flex-direction: column;
		pointer-events: auto;
	}
	
	/* 当loading为true时隐藏悬浮按钮 */
	:deep(.u-loading-page--show) ~ .float-buttons {
		display: none;
	}
	
	.float-btn {
		// width: 100upx;
		// height: 100upx;
		// border-radius: 50%;
		// background-color: rgba(43, 203, 212, 0.8);
		display: flex;
		flex-direction: column;
		align-items: end;
		justify-content: center;
		// margin-bottom: 20upx;
	}
	
	.float-icon {
		width: 200upx;
		// height: 200upx;
	}
	
	.float-text {
		font-size: 20upx;
		color: #fff;
		margin-top: 5upx;
	}
	
	/* 自定义标题栏样式 */
	.custom-title {
		position: relative;
		
		height: 67upx;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		margin: 20upx 30upx ;
	}
	
	.title-content {
		position: absolute;
		transform: translate(-50%, -50%);
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.title-eng-text {
		font-size: 24upx;
		margin-top: 10upx;
	}
	
	/* 创始人说模块样式 */
	.founder-container {
		padding: 0 30upx;
		margin-top: 10upx;
	}
	
	.founder-item {
		width: 100%;
		height: 400upx;
		position: relative;
		border-radius: 16upx;
		overflow: hidden;
		margin-bottom: 30upx;
		box-shadow: 0 8upx 20upx rgba(0, 0, 0, 0.15);
	}
	
	.founder-image {
		width: 100%;
		height: 100%;
		border-radius: 16upx;
		transition: transform 0.6s;
	}
	
	.founder-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.6));
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0.8;
		transition: opacity 0.3s ease;
	}
	
	.founder-play-icon {
		padding-left: 10upx;
		width: 100upx;
		height: 100upx;
		border-radius: 50%;
		// background: rgba(43, 203, 212, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		transform: scale(1);
		transition: transform 0.3s ease, background-color 0.3s ease;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}
	
	.founder-info {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 30upx;
		background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
		color: #fff;
	}
	
	.founder-title {
		font-size: 36upx;
		font-weight: bold;
		margin-bottom: 10upx;
		text-shadow: 0 2upx 4upx rgba(0,0,0,0.5);
	}
	
	.founder-digest {
		font-size: 26upx;
		line-height: 1.5;
		opacity: 0.85;
		white-space: pre-line;
	}
	
	/* 缩放效果 */
	.founder-item:active .founder-play-icon {
		transform: scale(1.1);
	}
	
	.founder-item:active .founder-image {
		transform: scale(1.05);
	}
	
	/* 产品网格样式 */
	.product-grid {
		display: flex;
		flex-wrap: wrap;
		padding: 0 10upx;
	}
	
	.product-grid-item {
		width: 50%;
		padding: 10upx;
	}
	
	.product-grid-image {
		width: 100%;
		height: 200upx;
		border-radius: 10upx;
	}
	
	.product-grid-name {
		font-size: 28upx;
		color: #333;
		margin-top: 10upx;
		display: block;
		text-align: center;
	}
	
	/* 默认模块样式 */
	.default-module {
		padding: 0 20upx;
	}
	
	.default-item {
		margin-bottom: 20upx;
		background-color: #fff;
		border-radius: 10upx;
		overflow: hidden;
	}
	
	.default-image {
		width: 100%;
		height: 300upx;
	}
	
	.default-title {
		padding: 20upx;
		font-size: 28upx;
		color: #333;
	}
	
	.intro-text {
		padding: 20upx;
		font-size: 28upx;
		line-height: 1.6;
		color: #666;
	}
	
	/* 运营管理模块 */
	.operation-container {
		padding: 0 20upx;
	}
	
	.operation-image {
		width: 100%;
		border-radius: 10upx;
	}
	
	/* 自定义指示器样式 */
	.cert-pagination {
		position: absolute;
		bottom: 20upx;
		left: 50%;
		transform: translateX(-50%);
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 10upx;
		padding: 5upx 10upx;
	}
	
	.cert-pagination-text {
		font-size: 24upx;
		color: #fff;
	}
	
	/* 模板41样式 - 特色内容 */
	.feature-container {
		padding: 0 30upx;
		margin-bottom: 20upx;
	}
	
	.feature-main-item {
		width: 100%;
		height: 330upx;
		position: relative;
		border-radius: 16upx;
		overflow: hidden;
		box-shadow: 0 8upx 16upx rgba(0, 0, 0, 0.12);
	}
	
	.feature-main-image {
		width: 100%;
		height: 100%;
		border-radius: 16upx;
		object-fit: cover;
		transition: transform 0.4s ease;
	}
	
	.feature-main-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		// background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.3));
	}
	
	.feature-main-play-icon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 80upx;
		height: 80upx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 6upx;
	}
	
	.feature-main-info {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 30upx;
		background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0));
	}
	
	.feature-main-title {
		font-size: 36upx;
		font-weight: bold;
		color: #fff;
		margin-bottom: 10upx;
		text-shadow: 0 2upx 4upx rgba(0,0,0,0.5);
	}
	
	.feature-main-digest {
		font-size: 26upx;
		color: #fff;
		opacity: 0.9;
		line-height: 1.5;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	.feature-scroll-container {
		padding: 0 30upx;
		margin-top: 20upx;
	}
	
	.feature-scroll-view {
		width: 100%;
	}
	
	.feature-scroll-list {
		display: flex;
		padding-bottom: 10upx;
	}
	
	.feature-scroll-item {
		width: 280upx;
		margin-right: 20upx;
		position: relative;
		flex-shrink: 0;
	}
	
	.feature-scroll-image {
		width: 280upx;
		height: 150upx;
		border-radius: 12upx;
		object-fit: cover;
	}
	
	.feature-scroll-play-icon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 50upx;
		height: 50upx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 4upx;
		z-index: 1;
		pointer-events: none;
	}
	
	.feature-scroll-title {
		font-size: 26upx;
		color: #333;
		margin-top: 10upx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	/* 点击效果 */
	.feature-main-item:active .feature-main-image {
		transform: scale(1.05);
	}
	
	/* 模板4样式 - 滑动卡片列表 */
	.slide-cards-container {
		padding: 0 30upx;
		margin-bottom: 30upx;
	}
	
	.slide-scroll-view {
		width: 100%;
	}
	
	.slide-card-list {
		display: flex;
		justify-content: space-between;
		padding: 10upx 0;
	}
	
	.slide-card-item {
		width: 260upx;
		margin-right: 20upx;
		position: relative;
		flex-shrink: 0;
		border-radius: 12upx;
		overflow: hidden;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
		background-color: #fff;
	}
	
	/* 单个卡片样式 - 撑满容器 */
	.slide-card-item-single {
		width: calc(100% - 40upx);
		margin-right: 0;
	}
	
	/* 双卡片样式 - 各占一半 */
	.slide-card-item-double {
		width: calc(50% - 15upx);
		margin-right: 20upx;
	}
	.slide-card-item-double:last-child {
		margin-right: 0;
	}
	
	.slide-card-image {
		width: 260upx;
		height: 352upx;
		object-fit: cover;
	}
	
	/* 单个卡片图片样式 */
	.slide-card-image-single {
		width: 100%;
	}
	
	/* 双卡片图片样式 */
	.slide-card-image-double {
		width: 100%;
	}
	
	.slide-card-play-icon {
		position: absolute;
		top: 220upx; /* 图片高度的一半 */
		left: 130upx; /* 卡片宽度的一半 */
		transform: translate(-50%, -50%);
		width: 60upx;
		height: 60upx;
		background: rgba(0, 0, 0, 0.4);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 4upx;
		pointer-events: none;
	}
	
	/* 调整单张和双张卡片时播放图标位置 */
	.slide-card-item-single .slide-card-play-icon,
	.slide-card-item-double .slide-card-play-icon {
		left: 50%;
	}
	
	.slide-card-title {
		margin-bottom: 10upx;
		font-size: 26upx;
		color: #333;
		padding: 16upx;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	/* 模板3样式 - 全部横向滚动小图 */
	.horizontal-scroll-container {
		padding: 0 30upx;
		margin: 10upx 0 30upx;
	}
	
	.horizontal-scroll-view {
		width: 100%;
	}
	
	.horizontal-scroll-list {
		display: flex;
		padding: 10upx 0;
	}
	
	.horizontal-scroll-item {
		width: 294upx;
		margin-right: 20upx;
		position: relative;
		flex-shrink: 0;
	}
	
	.horizontal-scroll-image {
		width: 294upx;
		height: 162upx;
		border-radius: 12upx;
		object-fit: cover;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
	}
	
	.horizontal-scroll-play-icon {
		position: absolute;
		top: 80upx; /* 图片高度的一半 */
		left: 147upx; /* 卡片宽度的一半 */
		transform: translate(-50%, -50%);
		width: 50upx;
		height: 50upx;
		background: rgba(0, 0, 0, 0.4);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 4upx;
		pointer-events: none;
	}
	
	.vr-icon {
		width: 30upx;
		height: 30upx;
		display: block;
	}
	
	.horizontal-scroll-title {
		font-size: 24upx;
		color: #333;
		margin-top: 10upx;
		text-align: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	.feature-vr-icon {
		width: 50upx;
		height: 32upx;
		display: block;
	}
	
	.feature-vr-icon-small {
		width: 40upx;
		height: 24upx;
		display: block;
	}
	
	/* 新闻资讯模块样式 */
	.news-container {
		padding: 0 30upx;
	}
	
	.news-item {
		margin-bottom: 30upx;
		padding: 30upx;
		background-color: #ffffff;
		border-radius: 20upx;
		box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.06);
		transition: transform 0.2s ease;
	}
	
	.news-item:active {
		transform: scale(0.98);
	}
	
	.news-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16upx;
	}
	
	.news-title-area {
		flex: 1;
		padding-right: 20upx;
	}
	
	.news-title {
		font-size: 32upx;
		font-weight: bold;
		color: #333;
		line-height: 1.4;
	}
	
	.news-image-container {
		width: 200upx;
		height: 140upx;
		border-radius: 12upx;
		overflow: hidden;
		flex-shrink: 0;
	}
	
	.news-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.news-brief {
		font-size: 26upx;
		color: #666;
		line-height: 1.5;
		margin: 20upx 0;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	
	.news-info-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 24upx;
		color: #999;
	}
	
	.news-author {
		display: flex;
		align-items: center;
	}
	
	.news-avatar {
		width: 40upx;
		height: 40upx;
		border-radius: 50%;
		margin-right: 10upx;
	}
	
	.news-meta {
		display: flex;
		align-items: center;
	}
	
	.news-date {
		margin-right: 20upx;
	}
	
	.news-stats {
		display: flex;
		align-items: center;
	}
	
	.news-views {
		margin-left: 6upx;
	}
	
	.news-load-more {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80upx;
		margin: 10upx 0 30upx;
	}
	
	.load-more-btn {
		background-color: #2BCBD4;
		border-radius: 40upx;
		padding: 10upx 30upx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.load-more-btn text {
		color: #fff;
		font-size: 24upx;
	}
	
	.loading {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.loading-text {
		font-size: 24upx;
		color: #999;
		margin-left: 10upx;
	}
	
	.no-more {
		font-size: 24upx;
		color: #999;
	}
	
	/* 直播未开始弹窗样式 */
	.live-not-started-popup {
		width: 600upx;
		background-color: #fff;
		border-radius: 16upx;
		overflow: hidden;
	}
	
	.live-popup-header {
		background: linear-gradient(135deg, #2BCBD4, #2573D9);
		padding: 30upx;
		text-align: center;
	}
	
	.live-popup-title {
		color: #fff;
		font-size: 32upx;
		font-weight: bold;
	}
	
	.live-popup-content {
		padding: 30upx;
	}
	
	.live-popup-cover {
		width: 100%;
		// height: 300upx;
		border-radius: 12upx;
		margin-bottom: 20upx;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
	}
	
	.live-popup-info {
		padding: 10upx 0;
	}
	
	.live-info-title {
		font-size: 30upx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16upx;
	}
	
	.live-info-meta {
		display: flex;
		align-items: center;
		margin-bottom: 10upx;
	}
	
	.live-info-author, .live-info-time {
		margin-left: 10upx;
		font-size: 24upx;
		color: #666;
	}
	
	.live-popup-status {
		background-color: #F8F8F8;
		padding: 20upx 30upx;
		display: flex;
		align-items: center;
		border-top: 1px solid #EEEEEE;
		border-bottom: 1px solid #EEEEEE;
	}
	
	.live-status-icon {
		width: 60upx;
		height: 60upx;
	}
	
	.live-status-text {
		margin-left: 20upx;
		font-size: 26upx;
		color: #FF5F5F;
	}
	
	.live-popup-footer {
		padding: 30upx;
		display: flex;
		justify-content: center;
	}
	
	.live-popup-btn {
		width: 80%;
		height: 80upx;
		background: linear-gradient(135deg, #2BCBD4, #2573D9);
		border-radius: 40upx;
		color: #fff;
		font-size: 28upx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4upx 12upx rgba(43, 203, 212, 0.3);
		transition: transform 0.2s ease;
	}
	
	.live-popup-btn:active {
		transform: scale(0.96);
	}
	
	/* 联系我们弹窗样式 */
	.contact-us-popup {
		width: 600upx;
		background-color: #fff;
		border-radius: 16upx;
		overflow: hidden;
	}
	
	.contact-popup-header {
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		padding: 30upx;
		text-align: center;
	}
	
	.contact-popup-title {
		color: #fff;
		font-size: 32upx;
		font-weight: bold;
	}
	
	.contact-popup-content {
		padding: 30upx;
	}
	
	.contact-popup-image {
		width: 100%;
		border-radius: 12upx;
		margin-bottom: 20upx;
		box-shadow: 0 4upx 12upx rgba(0, 0, 0, 0.1);
	}
	
	.contact-popup-info {
		padding: 10upx 0;
	}
	
	.contact-info-title {
		font-size: 30upx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16upx;
	}
	
	.contact-info-desc {
		font-size: 26upx;
		color: #666;
		line-height: 1.5;
		margin-bottom: 20upx;
	}
	
	.contact-info-meta {
		display: flex;
		align-items: center;
		margin-bottom: 10upx;
	}
	
	.contact-info-phone, .contact-info-wechat {
		margin-left: 10upx;
		font-size: 26upx;
		color: #666;
		font-weight: bold;
	}
	
	.contact-popup-footer {
		padding: 30upx;
		display: flex;
		justify-content: space-between;
	}
	
	.contact-popup-btn {
		width: 48%;
		height: 80upx;
		background: linear-gradient(135deg, #FFE8E0 0%, #FFF5F2 100%);
		border: 1px solid #FF6B35;
		border-radius: 40upx;
		color: #FF6B35;
		font-size: 28upx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: transform 0.2s ease;
	}
	
	.contact-popup-btn-call {
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		color: #fff;
		border: none;
		box-shadow: 0 4upx 12upx rgba(255, 107, 53, 0.3);
	}
	
	.contact-popup-btn:active {
		transform: scale(0.96);
	}
</style>