<template>
  <view class="container">
    <!-- 直播推流组件 -->
    <live-pusher
      id="livePusher"
      ref="livePusher"
      class="live-pusher"
      :url="pushUrl"
      :mode="mode"
      :muted="isMuted"
      :enable-camera="enableCamera"
      :auto-focus="autoFocus"
      :beauty="beauty"
      :whiteness="whiteness"
      :aspect="aspect"
      :device-position="devicePosition"
      :min-bitrate="minBitrate"
      :max-bitrate="maxBitrate"
      :audio-quality="audioQuality"
      :waiting-image="waitingImage"
      :zoom="false"
      :background-mute="false"
      @statechange="statechange"
      @netstatus="netstatus"
      @error="error"
    ></live-pusher>

    <!-- 顶部状态栏 -->
    <view class="top-bar" :style="topBarStyle">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="status-container">
        <view class="status-dot" :class="statusDotClass"></view>
        <text class="status-text">{{statusText}}</text>
      </view>
      <view class="time-container">
        <text class="time-text">{{formatTime(streamTime)}}</text>
      </view>
    </view>

    <!-- 右侧功能按钮 -->
    <view class="right-panel">
      <!-- 摄像头切换 -->
      <view class="function-btn" @click="switchCamera" :class="{disabled: switchingCamera}">
        <text class="btn-icon">⇄</text>
        <text class="btn-label">切换</text>
      </view>

      <!-- 美颜调节 -->
      <view class="function-btn" @click="showBeautyPanel = !showBeautyPanel">
        <text class="btn-icon">✨</text>
        <text class="btn-label">美颜</text>
        <text class="btn-value" v-if="beauty > 0">{{beauty}}</text>
      </view>

      <!-- 美白调节 -->
      <view class="function-btn" @click="showWhitenessPanel = !showWhitenessPanel">
        <text class="btn-icon">☀</text>
        <text class="btn-label">美白</text>
        <text class="btn-value" v-if="whiteness > 0">{{whiteness}}</text>
      </view>

      <!-- 静音切换 -->
      <view class="function-btn" @click="toggleMute">
        <text class="btn-icon" :class="isMuted ? 'muted' : ''">{{isMuted ? '🔇' : '🔊'}}</text>
        <text class="btn-label">{{isMuted ? '静音' : '音频'}}</text>
      </view>

      <!-- 设置 -->
      <view class="function-btn" @click="showSettingsPanel = !showSettingsPanel">
        <text class="btn-icon">⚙</text>
        <text class="btn-label">设置</text>
      </view>
    </view>

    <!-- 底部控制栏 -->
    <view class="bottom-bar">
      <!-- 预览按钮 -->
      <view class="control-btn secondary" @click="startPreview" v-if="!isPreviewing">
        <text class="control-btn-text">开始预览</text>
      </view>

      <!-- 停止预览按钮 -->
      <view class="control-btn secondary" @click="stopPreview" v-if="isPreviewing && !isPushing">
        <text class="control-btn-text">停止预览</text>
      </view>

      <!-- 开始推流按钮 -->
      <view class="control-btn primary" @click="start" v-if="isPreviewing && !isPushing && !isPaused">
        <text class="control-btn-text">开始直播</text>
      </view>

      <!-- 暂停按钮 -->
      <view class="control-btn warning" @click="pause" v-if="isPushing && !isPaused">
        <text class="control-btn-text">暂停直播</text>
      </view>

      <!-- 恢复按钮 - 只有在暂停状态下才显示 -->
      <view class="control-btn primary" @click="resume" v-if="isPaused && isPushing">
        <text class="control-btn-text">恢复直播</text>
      </view>

      <!-- 停止推流按钮 -->
      <view class="control-btn danger" @click="stop" v-if="isPushing">
        <text class="control-btn-text">结束直播</text>
      </view>
    </view>

    <!-- 美颜调节面板 -->
    <view class="beauty-panel" v-if="showBeautyPanel">
      <view class="panel-header">
        <text class="panel-title">美颜调节</text>
        <view class="close-btn" @click="showBeautyPanel = false">
          <text class="close-icon">✕</text>
        </view>
      </view>
      <view class="slider-container">
        <view class="slider-header">
          <text class="slider-label">美颜强度</text>
          <text class="slider-value">{{beauty}}/{{beautyMax}}</text>
        </view>
        <slider class="beauty-slider"
          :value="beauty"
          :min="0"
          :max="beautyMax"
          :step="1"
          @change="onBeautyChange"
          activeColor="#FF6B9D"
          backgroundColor="rgba(255,255,255,0.3)"
          block-size="28"
        />
      </view>
    </view>

    <!-- 美白调节面板 -->
    <view class="whiteness-panel" v-if="showWhitenessPanel">
      <view class="panel-header">
        <text class="panel-title">美白调节</text>
        <view class="close-btn" @click="showWhitenessPanel = false">
          <text class="close-icon">✕</text>
        </view>
      </view>
      <view class="slider-container">
        <view class="slider-header">
          <text class="slider-label">美白强度</text>
          <text class="slider-value">{{whiteness}}/{{whitenessMax}}</text>
        </view>
        <slider class="whiteness-slider"
          :value="whiteness"
          :min="0"
          :max="whitenessMax"
          :step="1"
          @change="onWhitenessChange"
          activeColor="#87CEEB"
          backgroundColor="rgba(255,255,255,0.3)"
          block-size="28"
        />
      </view>
    </view>

    <!-- 设置面板 -->
    <view class="settings-panel" v-if="showSettingsPanel">
      <view class="panel-header">
        <text class="panel-title">直播设置</text>
        <view class="close-btn" @click="showSettingsPanel = false">
          <text class="close-icon">✕</text>
        </view>
      </view>
      <view class="settings-content">
        <!-- 清晰度设置 -->
        <view class="setting-item">
          <text class="setting-label">清晰度</text>
          <view class="quality-options">
            <view class="quality-btn"
              v-for="option in qualityOptions"
              :key="option.value"
              :class="{active: mode === option.value}"
              @click="changeQuality(option.value)">
              <text class="quality-text">{{option.label}}</text>
            </view>
          </view>
        </view>

        <!-- 码率设置 -->
        <view class="setting-item">
          <text class="setting-label">码率范围</text>
          <view class="bitrate-container">
            <text class="bitrate-text">{{minBitrate}} - {{maxBitrate}} kbps</text>
          </view>
        </view>

        <!-- 网络状态 -->
        <view class="setting-item" @click="toggleNetInfo">
          <text class="setting-label">网络状态</text>
          <text class="setting-value">{{showNetInfo ? '显示中' : '已隐藏'}}</text>
        </view>

        <!-- iOS修复选项 -->
        <view class="setting-item" @click="manualFixIOSStuck" v-if="isIOS">
          <text class="setting-label">修复推流卡死</text>
          <text class="setting-value">点击修复</text>
        </view>

        <!-- iOS模拟后台切换 -->
        <view class="setting-item" @click="simulateBackgroundSwitch" v-if="isIOS">
          <text class="setting-label">模拟后台切换</text>
          <text class="setting-value">点击执行</text>
        </view>
      </view>
    </view>

    <!-- 网络状态信息 -->
    <view class="net-info" v-if="isPushing && showNetInfo">
      <view class="net-header">
        <text class="net-title">网络状态</text>
        <view class="net-close" @click="toggleNetInfo">
          <text class="close-icon">✕</text>
        </view>
      </view>
      <view class="net-content">
        <view class="net-item">
          <text class="net-label">视频码率</text>
          <text class="net-value">{{netStatus.videoBitrate || 0}} kbps</text>
        </view>
        <view class="net-item">
          <text class="net-label">音频码率</text>
          <text class="net-value">{{netStatus.audioBitrate || 0}} kbps</text>
        </view>
        <view class="net-item">
          <text class="net-label">视频帧率</text>
          <text class="net-value">{{netStatus.videoFPS || 0}} fps</text>
        </view>
        <view class="net-item">
          <text class="net-label">网络速度</text>
          <text class="net-value">{{netStatus.netSpeed || 0}} KB/s</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 推流地址 - 从页面参数获取
      pushUrl: "",

      // 直播间ID - 从页面参数获取
      liveId: "",

      // 直播开始时间 - 用于创建直播记录
      liveStartTime: null,

      // 视频设置
      mode: "FHD", // 推流视频模式，默认超清，可取值：SD（标清）, HD（高清）, FHD（超清）
      aspect: "9:16", // 视频宽高比例
      devicePosition: "front", // 摄像头位置，front前置，back后置
      minBitrate: 200, // 最小码率
      maxBitrate: 1000, // 最大码率

      // 控制参数
      isMuted: false, // 是否静音
      enableCamera: true, // 是否启用摄像头
      autoFocus: true, // 自动聚焦
      beauty: 0, // 美颜，iOS: 0-1, Android: 0-9
      whiteness: 0, // 美白，iOS: 0-1, Android: 0-9
      audioQuality: "high", // 音频质量
      waitingImage: "/static/syg/index/loading.png", // 等待画面

      // 平台相关设置
      isIOS: false, // 是否为iOS平台
      beautyMax: 9, // 美颜最大值
      whitenessMax: 9, // 美白最大值

      // 状态参数
      isPreviewing: false, // 是否在预览
      isPushing: false, // 是否在推流
      isPaused: false, // 是否暂停
      statusText: "准备中", // 状态文本
      switchingCamera: false, // 是否正在切换摄像头
      streamTime: 0, // 推流时长（秒）
      streamTimer: null, // 推流计时器

      // UI控制
      showBeautyPanel: false, // 显示美颜面板
      showWhitenessPanel: false, // 显示美白面板
      showSettingsPanel: false, // 显示设置面板
      showNetInfo: false, // 是否显示网络信息

      // 网络状态
      netStatus: {},

      // 视频质量选项
      qualityOptions: [
        { label: "标清", value: "SD" },
        { label: "高清", value: "HD" },
        { label: "超清", value: "FHD" }
      ],

      // 推流上下文
      context: null,
      // plus.video.LivePusher备用推流对象
      plusPusher: null,

      // 防抖控制
      lastUpdateTime: 0,
      updateDelay: 300, // 300ms防抖

      // 状态栏高度
      statusBarHeight: 0,

      // iOS后台状态记录
      wasPreviewingBeforeHide: false,
      wasPushingBeforeHide: false,
      wasPausedBeforeHide: false,

      // iOS卡死处理计数
      iosStuckCount: 0,

      // 页面卸载标记
      isPageUnloading: false,

      // 标记是否正在处理iOS卡死状态
      _handlingIOSStuck: false
    }
  },

  computed: {
    // 状态指示点样式
    statusDotClass() {
      if (this.isPushing && !this.isPaused) {
        return 'live';
      } else if (this.isPreviewing) {
        return 'preview';
      } else {
        return 'offline';
      }
    },

    // 顶部栏样式，包含状态栏高度
    topBarStyle() {
      return {
        paddingTop: this.statusBarHeight + 'px'
      };
    }
  },

  onLoad(options) {
    // 获取页面参数
    if (options.liveId) {
      this.liveId = options.liveId;
    }
    if (options.pushUrl) {
      this.pushUrl = decodeURIComponent(options.pushUrl);
    }

    console.log('直播页面参数:', { liveId: this.liveId, pushUrl: this.pushUrl });

    // 检查必要参数
    if (!this.pushUrl) {
      uni.showModal({
        title: '参数错误',
        content: '推流地址不能为空，请重新进入',
        showCancel: false,
        success: () => {
          uni.navigateBack();
        }
      });
      return;
    }

    // 获取状态栏高度
    // #ifdef APP-PLUS
    this.statusBarHeight = plus.navigator.getStatusbarHeight();
    // #endif
    // #ifndef APP-PLUS
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;
    // #endif

    // 检测平台并设置美颜美白范围
    // #ifdef APP-PLUS
    const platform = uni.getSystemInfoSync().platform;
    this.isIOS = platform === 'ios';
    if (this.isIOS) {
      this.beautyMax = 1;
      this.whitenessMax = 1;
      console.log("检测到iOS平台，设置相应参数");
    } else {
      this.beautyMax = 9;
      this.whitenessMax = 9;
    }
    // #endif

    // 监听页面返回
    const currentPage = this.$scope || this.$mp.page;
    if (currentPage && currentPage.$getAppWebview) {
      const appWebview = currentPage.$getAppWebview();
      appWebview.addEventListener('popGesture', (e) => {
        // 阻止iOS侧滑返回
        if (this.isPushing) {
          e.preventDefault();
          this.goBack(); // 调用自定义返回方法
        }
      });
    }

    // 设置Android物理返回键拦截
    // #ifdef APP-PLUS
    plus.key.addEventListener('backbutton', () => {
      if (this.isPushing) {
        this.goBack();
      } else {
        uni.navigateBack();
      }
    });
    // #endif
  },

  onReady() {
    console.log("页面onReady初始化推流上下文");
    // 在onReady中初始化推流上下文
    this.context = uni.createLivePusherContext("livePusher", this);

    // 简化iOS平台初始化，直接开始预览
    const delay = this.isIOS ? 1500 : 800;
    setTimeout(() => {
      // 直接使用startPreview方法，统一处理逻辑
      console.log("开始预览");
      this.startPreview();
    }, delay);
  },

  onShow() {
    console.log("页面显示");

    // 重置页面卸载标记
    this.isPageUnloading = false;

    // iOS平台从后台回来时，重置卡死计数器
    if (this.isIOS) {
      this.iosStuckCount = 0;
      console.log("重置iOS卡死计数器");

      // 重置处理中标记
      this._handlingIOSStuck = false;

      // 检查之前的状态
      console.log(`从后台恢复，状态检查：预览=${this.wasPreviewingBeforeHide}, 推流=${this.wasPushingBeforeHide}, 暂停=${this.wasPausedBeforeHide}`);
      
      // 强制设置当前状态为非推流状态，防止自动推流
      this.isPushing = false;
      
      // 重置预览状态
      this.isPreviewing = false;

      if (this.context) {
        // iOS从后台回来，重新开始预览
        console.log("iOS从后台回来，重新开始预览");
        
        // 重新创建上下文，确保摄像头资源完全释放再使用
        this.context = uni.createLivePusherContext("livePusher", this);
        
        // 延迟重新初始化预览
        setTimeout(() => {
          // 开始预览
          this.context.startPreview({
            success: () => {
              console.log("从后台恢复预览成功");
              this.isPreviewing = true;
              
              // 如果之前在推流且未暂停，提示用户手动恢复
              if (this.wasPushingBeforeHide && !this.wasPausedBeforeHide) {
                console.log("之前在推流，提示用户手动恢复");
                // 提示用户手动恢复
                uni.showModal({
                  title: '推流已暂停',
                  content: '由于切换到后台，直播已自动暂停，请点击恢复直播按钮继续',
                  showCancel: false,
                  success: () => {
                    // 设置为暂停状态，让用户手动恢复
                    this.isPaused = true;
                    this.isPushing = true;
                    console.log("状态已设置为暂停，等待用户手动恢复");
                  }
                });
              }
            },
            fail: () => {
              console.log("从后台恢复预览失败");
              // 尝试重置摄像头
              this.simulateBackgroundSwitch();
            }
          });
        }, 800);
      }
    }
  },

  onHide() {
    console.log("页面隐藏");

    // 如果是页面卸载，不执行后台暂停逻辑
    if (this.isPageUnloading) {
      console.log("页面正在卸载，跳过后台暂停逻辑");
      return;
    }

    // 记录当前状态，以便回来时恢复
    this.wasPreviewingBeforeHide = this.isPreviewing;
    this.wasPushingBeforeHide = this.isPushing;
    this.wasPausedBeforeHide = this.isPaused;
    
    console.log(`保存状态：预览=${this.wasPreviewingBeforeHide}, 推流=${this.wasPushingBeforeHide}, 暂停=${this.wasPausedBeforeHide}`);

    // 如果正在推流且未暂停，则自动暂停
    if (this.isPushing && !this.isPaused) {
      console.log("进入后台，自动暂停推流");
      // 直接设置状态，避免实际调用pause可能失败
      this.isPaused = true;
      
      // 如果是iOS平台，需要停止预览释放摄像头资源
      if (this.isIOS) {
        console.log("iOS平台进入后台，停止预览释放资源");
        this.context.stopPreview({
          complete: () => {
            console.log("后台停止预览完成");
          }
        });
      } else {
        // 安卓平台直接调用pause
        this.context.pause();
      }
    }
  },

  onUnload() {
    // 页面卸载时确保释放资源
    console.log("页面卸载，关闭所有资源");

    // 设置页面卸载标记
    this.isPageUnloading = true;

    // 清除计时器
    if (this.streamTimer) {
      clearInterval(this.streamTimer);
      this.streamTimer = null;
    }

    if (this.isPushing) {
      this.stop();
    }
    if (this.isPreviewing) {
      this.stopPreview();
    }

    // 释放plus推流对象
    // #ifdef APP-PLUS
    if (this.plusPusher) {
      this.stopPlusLivePusher();
    }
    // #endif

    // 移除Android返回键监听
    // #ifdef APP-PLUS
    plus.key.removeEventListener('backbutton');
    // #endif
  },
  
  methods: {
    // 格式化时间显示
    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;

      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
    },

    // 开始计时
    startTimer() {
      if (this.streamTimer) {
        clearInterval(this.streamTimer);
      }
      this.streamTime = 0;
      this.streamTimer = setInterval(() => {
        this.streamTime++;
      }, 1000);
    },

    // 停止计时
    stopTimer() {
      if (this.streamTimer) {
        clearInterval(this.streamTimer);
        this.streamTimer = null;
      }
      this.streamTime = 0;
    },

    // 返回上一页
    goBack() {
      if (this.isPushing) {
        uni.showModal({
          title: '结束直播',
          content: '正在直播中，确定要结束直播吗？',
          showCancel: true,
          cancelText: '继续直播',
          confirmText: '结束直播',
          confirmColor: '#FF3B30',
          success: (res) => {
            if (res.confirm) {
              console.log("用户确认结束直播");
              this.stop();
              setTimeout(() => {
                console.log("返回上一页");
                uni.navigateBack();
              }, 500);
            }
          }
        });
      } else {
        uni.navigateBack();
      }
    },

    // 美颜调节
    onBeautyChange(e) {
      const value = e.detail.value;
      this.updateBeauty(value);
    },

    // 美白调节
    onWhitenessChange(e) {
      const value = e.detail.value;
      this.updateWhiteness(value);
    },

    // 防抖更新美颜
    updateBeauty(value) {
      this.beauty = value;
      const now = Date.now();
      this.lastUpdateTime = now;

      setTimeout(() => {
        if (Date.now() - this.lastUpdateTime >= this.updateDelay - 50) {
          this.applyBeautySettings();
        }
      }, this.updateDelay);
    },

    // 防抖更新美白
    updateWhiteness(value) {
      this.whiteness = value;
      const now = Date.now();
      this.lastUpdateTime = now;

      setTimeout(() => {
        if (Date.now() - this.lastUpdateTime >= this.updateDelay - 50) {
          this.applyBeautySettings();
        }
      }, this.updateDelay);
    },

    // 应用美颜设置
    applyBeautySettings() {
      if (!this.context) return;

      // 通过重新设置组件属性来应用美颜设置
      this.$nextTick(() => {
        console.log(`应用美颜设置: 美颜=${this.beauty}, 美白=${this.whiteness}`);
        // 强制更新组件
        this.$forceUpdate();
      });
    },

    // 处理iOS摄像头卡死状态
    handleIOSCaptureStuck() {
      console.log("开始处理iOS摄像头卡死状态");

      // 记录处理次数，避免无限循环
      if (!this.iosStuckCount) {
        this.iosStuckCount = 0;
      }
      this.iosStuckCount++;

      if (this.iosStuckCount > 3) {
        console.log("iOS卡死处理次数过多，停止自动处理");
        uni.showModal({
          title: '推流异常',
          content: '检测到推流异常，请尝试切换到后台再回来，或重新进入页面',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }

      // 强制重置预览状态
      this.isPreviewing = false;
      console.log("强制重置预览状态为false");

      // 先停止当前的预览和推流
      this.context.stopPreview({
        complete: () => {
          console.log("停止预览完成");
          // 强制重置状态
          this.isPreviewing = false;

          // 重新创建推流上下文
          setTimeout(() => {
            console.log("重新创建推流上下文");
            this.context = uni.createLivePusherContext("livePusher", this);

            // 延迟重新开始预览
            setTimeout(() => {
              console.log("重新开始预览");
              this.forceStartPreview();
            }, 1000);
          }, 500);
        }
      });
    },

    // 强制开始预览（忽略状态检查）
    forceStartPreview() {
      console.log("强制开始预览，忽略状态检查");

      if (!this.context) {
        this.context = uni.createLivePusherContext("livePusher", this);
      }

      // 强制重置状态
      this.isPreviewing = false;

      // 先停止可能存在的预览
      this.context.stopPreview({
        complete: () => {
          console.log("强制预览前清理完成");
          // 延迟启动预览，给iOS更多时间释放摄像头资源
          setTimeout(() => {
            // 直接开始预览，不做任何状态检查
            this.context.startPreview({
              success: (res) => {
                console.log("强制预览成功:" + JSON.stringify(res));
                this.isPreviewing = true;
                this.statusText = "预览中";
              },
              fail: (err) => {
                console.log("强制预览失败:" + JSON.stringify(err));
                // 预览失败，延迟更久再尝试
                setTimeout(() => {
                  console.log("强制预览再次尝试");
                  this.context.startPreview({
                    success: (res) => {
                      console.log("强制预览重试成功");
                      this.isPreviewing = true;
                      this.statusText = "预览中";
                    },
                    fail: (err) => {
                      console.log("强制预览重试仍然失败");
                      this.isPreviewing = false;
                      // 多次失败，提示用户等待
                      uni.showToast({
                        title: '请稍等片刻再尝试开始直播',
                        icon: 'none',
                        duration: 3000
                      });
                    }
                  });
                }, 2000);
              }
            });
          }, 1000);
        }
      });
    },

    // 切换网络信息显示
    toggleNetInfo() {
      this.showNetInfo = !this.showNetInfo;
    },

    // 切换视频质量
    changeQuality(quality) {
      if (this.mode === quality) return;

      this.mode = quality;
      const option = this.qualityOptions.find(item => item.value === quality);
      uni.showToast({
        title: `已切换至${option.label}`,
        icon: 'none'
      });

      // 关闭设置面板
      this.showSettingsPanel = false;

      // 如果正在推流，需要重新应用设置
      if (this.isPushing) {
        this.applyQualitySettings();
      }
    },

    // 应用画质设置
    applyQualitySettings() {
      // 通过强制更新组件来应用新的画质设置
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    // 切换静音
    toggleMute() {
      this.isMuted = !this.isMuted;
      uni.showToast({
        title: this.isMuted ? '已静音' : '已取消静音',
        icon: 'none',
        duration: 1500
      });

      // 立即应用静音设置
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    // 手动修复iOS推流卡死（供用户在设置中调用）
    manualFixIOSStuck() {
      if (!this.isIOS) {
        uni.showToast({
          title: '此功能仅适用于iOS设备',
          icon: 'none'
        });
        return;
      }

      console.log("手动修复iOS推流卡死");
      uni.showLoading({
        title: '正在修复...'
      });

      // 重置计数器和状态
      this.iosStuckCount = 0;
      this.isPreviewing = false;
      this.isPushing = false;
      this.isPaused = false;

      // 重新创建上下文
      this.context = uni.createLivePusherContext("livePusher", this);

      // 延迟强制启动预览
      setTimeout(() => {
        this.forceStartPreview();

        setTimeout(() => {
          uni.hideLoading();
          uni.showToast({
            title: '修复完成，请重试推流',
            icon: 'none'
          });
        }, 1000);
      }, 1000);
    },

    // 模拟后台切换修复iOS推流（终极解决方案）
    simulateBackgroundSwitch() {
      console.log("模拟后台切换修复iOS推流");

      // 记录当前状态
      const wasPreviewing = this.isPreviewing;
      const wasPushing = this.isPushing;
      
      // 向用户显示正在修复的提示
      uni.showLoading({
        title: '正在修复推流...',
        mask: true
      });
      
      // 重置所有状态
      this.isPreviewing = false;
      this.isPushing = false;
      this.isPaused = false;
      this.statusText = "正在修复...";

      // 先停止所有活动
      this.context.stopPreview({
        complete: () => {
          console.log("停止预览完成");
          
          // 销毁并重新创建上下文
          setTimeout(() => {
            console.log("重新创建推流上下文");
            this.context = null;
            this.context = uni.createLivePusherContext("livePusher", this);
            
            // 延迟启动预览
            setTimeout(() => {
              console.log("重新启动预览");
              
              this.context.startPreview({
                success: (res) => {
                  console.log("重启预览成功");
                  this.isPreviewing = true;
                  this.statusText = "预览中";
                  
                  // 隐藏加载提示
                  uni.hideLoading();
                  
                  // 如果之前在推流，提示用户重新开始
                  if (wasPushing) {
                    uni.showModal({
                      title: '预览已恢复',
                      content: '摄像头已重置，请点击"开始直播"按钮重新开始',
                      showCancel: false
                    });
                  } else {
                    uni.showToast({
                      title: '修复完成，可以开始直播',
                      icon: 'none',
                      duration: 2000
                    });
                  }
                },
                fail: (err) => {
                  console.log("重启预览失败:" + JSON.stringify(err));
                  
                  // 隐藏加载提示
                  uni.hideLoading();
                  
                  uni.showModal({
                    title: '修复失败',
                    content: '请退出页面重试或重启应用',
                    showCancel: false
                  });
                }
              });
            }, 1000);
          }, 1000);
        }
      });
    },
    
    // 状态变化事件处理
    statechange(e) {
      console.log("statechange:" + JSON.stringify(e));
      const code = e.detail.code;
      const message = e.detail.message || '';

      // 记录所有状态变化，帮助调试
      console.log(`推流状态变化: 代码=${code}, 消息=${message}`);

      // iOS平台特殊处理：首次进入时的1010状态码处理
      if (this.isIOS && code === 1010 && message === "Capturer Living...") {
        console.log("iOS首次进入状态，检测是否需要重新初始化");
        
        // 如果已经在推流，则不需要特殊处理
        if (this.isPushing) {
          console.log("已在推流中，跳过特殊处理");
          return;
        }
        
        // 避免重复处理
        if (this._handlingIOSStuck) {
          console.log("已在处理iOS卡死状态，跳过");
          return;
        }
        
        this._handlingIOSStuck = true;
        
        // 延迟一段时间再进行处理，避免过早判断为卡死
        setTimeout(() => {
          console.log("延迟检测iOS状态");
          // 如果未推流成功，且仍然在预览状态，则可能是卡死状态
          if (!this.isPushing && this.isPreviewing) {
            console.log("检测到iOS推流卡死，尝试模拟后台切换");
            this.simulateBackgroundSwitch();
          } else {
            console.log("iOS状态正常，无需特殊处理");
          }
          this._handlingIOSStuck = false;
        }, 5000); // 延长检测时间到5秒，避免误判
        return;
      }

      switch (code) {
        case 1001:
          this.statusText = "连接服务器";
          break;
        case 1002:
          this.statusText = "开始推流";
          this.isPushing = true;
          this.isPaused = false;
          this.startTimer(); // 开始计时

          // 记录直播开始时间 - 格式：yyyy-MM-dd HH:mm:ss
          const now = new Date();
          this.liveStartTime = now.getFullYear() + '-' +
            String(now.getMonth() + 1).padStart(2, '0') + '-' +
            String(now.getDate()).padStart(2, '0') + ' ' +
            String(now.getHours()).padStart(2, '0') + ':' +
            String(now.getMinutes()).padStart(2, '0') + ':' +
            String(now.getSeconds()).padStart(2, '0');
          console.log('直播开始时间:', this.liveStartTime);

          // 推流成功，重置iOS卡死计数器
          if (this.isIOS) {
            this.iosStuckCount = 0;
          }
          uni.showToast({
            title: '直播开始',
            icon: 'success',
            duration: 2000
          });
          break;
        case 1003:
          this.statusText = "直播中";
          this.isPushing = true;
          break;
        case 1004:
          this.statusText = "直播暂停";
          this.isPaused = true;
          // 暂停时不停止推流连接，只是暂停画面
          break;
        case 1005:
          this.statusText = "直播恢复";
          this.isPaused = false;
          break;
        case 1006:
          this.statusText = "直播停止";
          this.isPushing = false;
          this.isPaused = false;
          this.stopTimer(); // 停止计时
          break;
        case 1007:
          this.statusText = "直播结束";
          this.isPushing = false;
          this.isPaused = false;
          this.stopTimer(); // 停止计时
          break;
        case 1008:
          this.statusText = "预览中";
          this.isPreviewing = true;
          break;
        case 1009:
          this.statusText = "预览停止";
          this.isPreviewing = false;
          break;
        case 1010:
          // iOS平台的特殊状态处理
          if (this.isIOS && !this.isPushing) {
            this.statusText = "准备中";
          } else {
            this.statusText = "推流中";
            this.isPushing = true;
          }
          break;
        case -1301:
          this.statusText = "摄像头权限被拒绝";
          uni.showModal({
            title: '权限提示',
            content: '需要摄像头权限才能进行直播，请在设置中开启摄像头权限',
            showCancel: false,
            confirmText: '知道了'
          });
          break;
        case -1302:
          this.statusText = "麦克风权限被拒绝";
          uni.showModal({
            title: '权限提示',
            content: '需要麦克风权限才能进行直播，请在设置中开启麦克风权限',
            showCancel: false,
            confirmText: '知道了'
          });
          break;
        case -1307:
          this.statusText = "网络连接失败";
          uni.showToast({
            title: '网络连接失败，请检查网络',
            icon: 'none',
            duration: 3000
          });
          break;
        case 3001:
        case 3002:
        case 3003:
        case 3004:
        case 3005:
          this.statusText = "服务器连接错误";
          uni.showToast({
            title: '服务器连接错误，请稍后重试',
            icon: 'none',
            duration: 3000
          });
          this.isPushing = false;
          this.stopTimer();
          break;
        default:
          this.statusText = message || `状态码: ${code}`;
      }
    },
    
    // 网络状态事件处理
    netstatus(e) {
      console.log("netstatus:" + JSON.stringify(e));
      this.netStatus = e.detail || {};
    },

    // 错误事件处理
    error(e) {
      console.log("error:" + JSON.stringify(e));
      const errMsg = e.detail?.errMsg || e.detail?.message || '未知错误';
      uni.showToast({
        title: `错误：${errMsg}`,
        icon: 'none',
        duration: 3000
      });

      // 错误时重置状态
      this.isPushing = false;
      this.isPaused = false;
      this.stopTimer();
    },

    // 开始推流
    start() {
      console.log("开始推流，地址：" + this.pushUrl);

      // 检查推流地址
      if (!this.pushUrl) {
        uni.showToast({
          title: '推流地址不能为空',
          icon: 'none'
        });
        return;
      }

      // 确保预览已经开始
      if (!this.isPreviewing) {
        console.log("预览未开始，先开始预览");
        this.startPreview();
        
        // 等待预览成功后再推流
        setTimeout(() => {
          if (this.isPreviewing) {
            this.doStartPush();
          } else {
            uni.showToast({
              title: '预览启动失败',
              icon: 'none'
            });
          }
        }, 1000);
        return;
      }

      this.doStartPush();
    },

    // 执行推流操作
    doStartPush() {
      console.log("执行推流操作");
      
      // iOS平台先确保预览状态正常
      if (this.isIOS && !this.isPreviewing) {
        console.log("iOS平台先确保预览状态");
        this.startPreview();
        setTimeout(() => {
          if (this.isPreviewing) {
            this.actualStartPush();
          } else {
            uni.showToast({
              title: '预览启动失败',
              icon: 'none'
            });
          }
        }, 1500);
        return;
      }
      
      this.actualStartPush();
    },
    
    // 实际执行推流操作
    actualStartPush() {
      console.log("实际执行推流操作");
      
      // 确保上下文存在
      if (!this.context) {
        console.log("推流上下文不存在，重新创建");
        this.context = uni.createLivePusherContext("livePusher", this);
      }
      
      console.log("开始调用推流API");
      
      // 简化推流操作，直接调用start方法
      try {
        this.context.start({
          success: (res) => {
            console.log("推流成功:" + JSON.stringify(res));
            this.isPushing = true;
            this.isPaused = false;
            this.statusText = "直播中";
            this.startTimer();
            
            uni.showToast({
              title: '直播开始',
              icon: 'success',
              duration: 2000
            });
          },
          fail: (err) => {
            console.log("推流失败:" + JSON.stringify(err));
            
            // 推流失败时，检查是否是iOS平台
            if (this.isIOS) {
              console.log("iOS平台推流失败，直接使用模拟后台切换方法修复");
              this.simulateBackgroundSwitch();
            } else {
              uni.showToast({
                title: '推流失败，请检查网络',
                icon: 'none',
                duration: 2000
              });
            }
          },
          complete: () => {
            console.log("推流API调用完成");
          }
        });
      } catch (e) {
        console.log("推流API调用异常:" + JSON.stringify(e));
        uni.showToast({
          title: '推流发生异常',
          icon: 'none',
          duration: 2000
        });
      }
    },
    
    // 暂停推流
    pause() {
      if (!this.isPushing || this.isPaused) {
        uni.showToast({
          title: '当前未在推流或已暂停',
          icon: 'none'
        });
        return;
      }

      console.log("开始暂停推流");
      this.statusText = "暂停中...";

      // 直接设置状态为暂停
      this.isPaused = true;
      // 保持isPushing为true，表示仍在推流状态但已暂停
      this.statusText = "直播暂停";
      
      // 停止预览，释放摄像头资源
      this.context.stopPreview({
        success: (res) => {
          console.log("暂停推流成功:" + JSON.stringify(res));
          
          uni.showToast({
            title: '直播已暂停',
            icon: 'none',
            duration: 2000
          });
        },
        fail: (err) => {
          console.log("暂停推流停止预览失败:" + JSON.stringify(err));
          // 即使失败也设置为暂停状态
        }
      });
    },

    // 恢复推流
    resume() {
      if (!this.isPaused) {
        uni.showToast({
          title: '当前未暂停',
          icon: 'none'
        });
        return;
      }

      console.log("开始恢复推流");
      this.statusText = "恢复中...";

      // 确保预览已启动
      this.context.startPreview({
        success: () => {
          console.log("恢复推流预览成功");
          
          // 预览成功后再开始推流
          setTimeout(() => {
            this.isPaused = false;
            this.isPreviewing = true;
            
            console.log("预览成功，开始恢复推流");
            
            this.context.start({
              success: (res) => {
                console.log("恢复推流成功:" + JSON.stringify(res));
                this.isPushing = true;
                this.statusText = "直播中";
                
                uni.showToast({
                  title: '直播已恢复',
                  icon: 'success',
                  duration: 2000
                });
              },
              fail: (err) => {
                console.log("恢复推流失败:" + JSON.stringify(err));
                
                // 恢复失败时，仍保持暂停状态
                this.isPaused = true;
                this.statusText = "直播暂停";
                
                uni.showToast({
                  title: '恢复失败，请重试',
                  icon: 'none'
                });
              }
            });
          }, 1000);
        },
        fail: (err) => {
          console.log("恢复预览失败:" + JSON.stringify(err));
          uni.showToast({
            title: '恢复失败，请重试',
            icon: 'none'
          });
        }
      });
    },

    // 停止推流
    stop() {
      if (!this.isPushing && !this.isPaused) {
        uni.showToast({
          title: '当前未在推流',
          icon: 'none'
        });
        return;
      }

      console.log("开始停止推流");

      // 先重置状态，确保UI立即更新
      const wasPaused = this.isPaused;
      this.isPushing = false;
      this.isPaused = false;
      this.statusText = "正在停止...";
      this.stopTimer();

      // 如果是从暂停状态停止，直接处理
      if (wasPaused) {
        console.log("从暂停状态停止推流");

        // 创建直播记录
        this.createLiveHistory();

        uni.showToast({
          title: '直播已结束',
          icon: 'success',
          duration: 2000
        });
        this.statusText = "直播结束";

        // 保持预览状态
        setTimeout(() => {
          this.statusText = "预览中";
        }, 1000);
        return;
      }

      // 正常停止推流
      this.context.stop({
        success: (res) => {
          console.log("livePusher.stop 成功:" + JSON.stringify(res));

          // 创建直播记录
          this.createLiveHistory();

          uni.showToast({
            title: '直播已结束',
            icon: 'success',
            duration: 2000
          });
          this.statusText = "直播结束";

          // 延迟一下再更新状态，保持预览
          setTimeout(() => {
            this.statusText = "预览中";
          }, 1000);
        },
        fail: (err) => {
          console.log("livePusher.stop 失败:" + JSON.stringify(err));
          this.statusText = "停止失败";

          // 即使停止失败也尝试创建直播记录
          this.createLiveHistory();

          uni.showToast({
            title: '停止推流失败',
            icon: 'none'
          });

          // 失败时也保持预览状态
          setTimeout(() => {
            this.statusText = "预览中";
          }, 1000);
        }
      });
    },
    


    // 开始预览
    startPreview() {
      console.log("开始预览");

      if (!this.context) {
        this.context = uni.createLivePusherContext("livePusher", this);
        console.log("重新创建推流上下文");
      }

      // 简化预览逻辑，直接调用API
      this.context.startPreview({
        success: (res) => {
          console.log("预览成功:" + JSON.stringify(res));
          this.isPreviewing = true;
          this.statusText = "预览中";
          
          // 取消自动推流，要求用户手动点击
          /* 
          if (this.isIOS && !this.isPushing) {
            console.log("iOS平台预览成功，尝试自动启动推流");
            setTimeout(() => {
              if (!this.isPushing) {
                console.log("自动开始推流");
                this.doStartPush();
              }
            }, 2000); // 延迟2秒后尝试推流
          }
          */
        },
        fail: (err) => {
          console.log("预览失败:" + JSON.stringify(err));
          this.isPreviewing = false;
          
          // 预览失败重试一次
          setTimeout(() => {
            console.log("预览失败，尝试重试");
            this.context.startPreview({
              success: (res) => {
                console.log("预览重试成功");
                this.isPreviewing = true;
                this.statusText = "预览中";
              },
              fail: (err) => {
                console.log("预览重试仍然失败");
                uni.showModal({
                  title: '摄像头启动失败',
                  content: '请检查摄像头权限或重启应用',
                  showCancel: false
                });
              }
            });
          }, 1000);
        }
      });
    },

    // 处理iOS预览失败
    handleIOSPreviewFailed() {
      console.log("处理iOS预览失败");
      // 重新创建上下文
      this.context = uni.createLivePusherContext("livePusher", this);

      // 延迟重试
      setTimeout(() => {
        this.context.startPreview({
          success: (res) => {
            console.log("iOS预览重试成功");
            this.isPreviewing = true;
            this.statusText = "预览中";
          },
          fail: (err) => {
            console.log("iOS预览重试仍然失败");
            uni.showModal({
              title: '预览失败',
              content: '摄像头启动失败，请检查权限或重启应用',
              showCancel: false,
              confirmText: '知道了'
            });
          }
        });
      }, 1000);
    },

    // 停止预览
    stopPreview() {
      if (!this.isPreviewing) {
        uni.showToast({
          title: '当前未在预览',
          icon: 'none'
        });
        return;
      }

      this.context.stopPreview({
        success: (res) => {
          console.log("livePusher.stopPreview 成功:" + JSON.stringify(res));
          uni.showToast({
            title: '预览停止',
            icon: 'none',
            duration: 1500
          });
          this.isPreviewing = false;
          this.statusText = "准备中";
        },
        fail: (err) => {
          console.log("停止预览失败:" + JSON.stringify(err));
          // 即使失败也重置状态
          this.isPreviewing = false;
          this.statusText = "准备中";
        }
      });
    },
    
    // 切换摄像头
    switchCamera() {
      // 标记摄像头切换中，避免连续切换
      if (this.switchingCamera) {
        uni.showToast({
          title: '正在切换摄像头...',
          icon: 'none'
        });
        return;
      }

      this.switchingCamera = true;

      this.context.switchCamera({
        success: (res) => {
          console.log("livePusher.switchCamera 成功:" + JSON.stringify(res));

          // 切换设备位置状态
          this.devicePosition = this.devicePosition === 'front' ? 'back' : 'front';

          uni.showToast({
            title: `已切换到${this.devicePosition === 'front' ? '前置' : '后置'}摄像头`,
            icon: 'none',
            duration: 2000
          });

          // iOS平台切换摄像头后，检查预览是否正常
          if (this.isIOS) {
            setTimeout(() => {
              // 如果预览黑屏，强制重新启动预览
              console.log("iOS摄像头切换后，检查预览状态");
              if (!this.isPushing) {
                // 只有在非推流状态下才重新启动预览
                this.forceStartPreview();
              }
            }, 500);
          }

          this.switchingCamera = false;
        },
        fail: (err) => {
          console.error('切换摄像头失败:', err);
          uni.showToast({
            title: '切换摄像头失败',
            icon: 'none'
          });
          this.switchingCamera = false;
        }
      });
    },

    // iOS平台资源预热
    preWarmIOSCamera() {
      console.log("iOS平台资源预热");
      // 尝试启动预览，但不推流，以预热摄像头资源
      this.context.startPreview({
        success: (res) => {
          console.log("iOS预览预热成功");
          // 预览成功后，立即停止，避免占用资源
          this.context.stopPreview({
            complete: () => {
              console.log("iOS预览预热完成");
            }
          });
        },
        fail: (err) => {
          console.log("iOS预览预热失败:" + JSON.stringify(err));
          // 预览失败也继续，因为目的是预热
        }
      });
    },

    // 创建直播记录
    async createLiveHistory() {
      try {
        // 检查必要参数
        if (!this.liveId || !this.liveStartTime) {
          console.log('创建直播记录失败：缺少必要参数', {
            liveId: this.liveId,
            startTime: this.liveStartTime
          });
          return;
        }

        console.log('开始创建直播记录...');

        // 调用创建直播记录接口
        const response = await this.syghttp.ajax({
          url: this.syghttp.api.apiCreateLiveHistory,
          method: 'POST',
          data: {
            liveId: this.liveId,
            startTime: this.liveStartTime
            // companyId 会自动添加
          }
        });

        if (response.code === 1000) {
          console.log('直播记录创建成功:', response);
        } else {
          console.error('直播记录创建失败:', response.msg);
        }

      } catch (error) {
        console.error('创建直播记录异常:', error);
      }
    }
  }
}
</script>

<style>
/* 基础容器 */
.container {
  flex: 1;
  position: relative;
  background-color: #000000;
}

.live-pusher {
  flex: 1;
  width: 750rpx;
  height: 100vh;
  background-color: #000000;
}

/* 顶部状态栏 */
.top-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 30rpx 20rpx;
  background: linear-gradient(to bottom, rgba(0,0,0,0.8), rgba(0,0,0,0));
  z-index: 100;
}

.back-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
  justify-content: center;
  align-items: center;
}

.back-icon {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: bold;
}

.close-icon {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: bold;
}

.status-container {
  flex-direction: row;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 8rpx;
  margin-right: 12rpx;
}

.status-dot.offline {
  background-color: #8E8E93;
}

.status-dot.preview {
  background-color: #FF9500;
}

.status-dot.live {
  background-color: #FF3B30;
}

.status-text {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
}

.time-container {
  background-color: rgba(0, 0, 0, 0.6);
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
}

.time-text {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

/* 右侧功能面板 */
.right-panel {
  position: absolute;
  top: 200rpx;
  right: 30rpx;
  width: 120rpx;
}

.function-btn {
  width: 120rpx;
  height: 120rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 60rpx;
  justify-content: center;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
}

.function-btn.disabled {
  opacity: 0.5;
}

.btn-icon {
  color: #FFFFFF;
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.btn-icon.muted {
  color: #FF3B30;
}

.btn-label {
  color: #FFFFFF;
  font-size: 20rpx;
  text-align: center;
}

.btn-value {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 36rpx;
  height: 36rpx;
  background-color: #FF6B9D;
  border-radius: 18rpx;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 36rpx;
  color: #FFFFFF;
  font-size: 20rpx;
  font-weight: bold;
}

/* 底部控制栏 */
.bottom-bar {
  position: absolute;
  bottom: 80rpx;
  left: 0;
  right: 0;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0 60rpx;
}

.control-btn {
  height: 88rpx;
  padding: 0 48rpx;
  border-radius: 44rpx;
  justify-content: center;
  align-items: center;
  margin: 0 16rpx;
}

.control-btn.primary {
  background-color: #007AFF;
}

.control-btn.secondary {
  background-color: rgba(142, 142, 147, 0.8);
}

.control-btn.warning {
  background-color: #FF9500;
}

.control-btn.danger {
  background-color: #FF3B30;
}

.control-btn-text {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
}

/* 调节面板样式 */
.beauty-panel,
.whiteness-panel,
.settings-panel {
  position: absolute;
  bottom: 200rpx;
  left: 30rpx;
  right: 30rpx;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 24rpx;
  padding: 40rpx;
}

.panel-header {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.panel-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(142, 142, 147, 0.3);
  justify-content: center;
  align-items: center;
}

.slider-container {
  margin-bottom: 20rpx;
}

.slider-header {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.slider-label {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
}

.slider-value {
  color: #FF6B9D;
  font-size: 28rpx;
  font-weight: 600;
}

.beauty-slider,
.whiteness-slider {
  width: 100%;
  height: 80rpx;
}

/* 设置面板内容 */
.settings-content {

}

.setting-item {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.setting-label {
  color: #FFFFFF;
  font-size: 32rpx;
}

.setting-value {
  color: #8E8E93;
  font-size: 28rpx;
}

.quality-options {
  flex-direction: row;
}

.quality-btn {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background-color: rgba(142, 142, 147, 0.3);
  margin-left: 16rpx;
}

.quality-btn.active {
  background-color: #007AFF;
}

.quality-text {
  color: #FFFFFF;
  font-size: 24rpx;
}

.bitrate-container {

}

.bitrate-text {
  color: #8E8E93;
  font-size: 28rpx;
}

/* 网络状态信息 */
.net-info {
  position: absolute;
  left: 30rpx;
  bottom: 200rpx;
  width: 400rpx;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 24rpx;
  padding: 32rpx;
}

.net-header {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.net-title {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
}

.net-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background-color: rgba(142, 142, 147, 0.3);
  justify-content: center;
  align-items: center;
}

.net-content {

}

.net-item {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.net-label {
  color: #8E8E93;
  font-size: 28rpx;
}

.net-value {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
}


</style>