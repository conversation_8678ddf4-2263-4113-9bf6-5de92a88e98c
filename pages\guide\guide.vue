<template>
	<view class="guide-container">
		<!-- 毛玻璃背景效果 -->
		<view class="blur-background"></view>
		
		<!-- 跳过按钮 -->
		<view class="skip-btn" @click="skipGuide">跳过</view>
		
		<!-- 轮播图 -->
		<swiper class="swiper" :indicator-dots="true" 
			:autoplay="false" :interval="3000" :duration="500" 
			:circular="false" @change="handleSwiperChange"
			:current="currentIndex"
			indicator-color="rgba(255, 255, 255, 0.6)"
			indicator-active-color="#FFFFFF"
			:indicator-style="indicatorStyle"
			:indicator-active-style="indicatorActiveStyle"
			easing-function="easeInOutCubic">
			<swiper-item v-for="(item, index) in guideImages" :key="index" class="swiper-item">
				<view class="content-wrapper">
					<image :src="item" mode="widthFix" class="guide-image"></image>
					<!-- <view class="text-content">
						<text class="title">{{ guideTitles[index] }}</text>
						<text class="desc">{{ guideDescriptions[index] }}</text>
					</view> -->
				</view>
			</swiper-item>
		</swiper>
		
		<!-- 下一步按钮 -->
		<view class="next-btn" v-if="currentIndex < guideImages.length - 1" @click="nextSlide">
			下一步
		</view>
		
		<!-- 开始使用按钮（最后一页显示） -->
		<view class="start-btn" v-if="currentIndex === guideImages.length - 1" @click="skipGuide">
			开始使用
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentIndex: 0,
				guideImages: [
					'/static/image/yd1.png',
					'/static/image/yd3.png',
					'/static/image/yd2.png'
				],
				guideTitles: [
					'欢迎来到勃学超市',
					'智能学习平台',
						'轻松了解孩子',
					'开启学习之旅'
				],
				guideDescriptions: [
					'全新学习体验，为您提供优质教育内容',
					'AI智能助手，帮助您解决学习难题',
					'学勤，服务一览无余',
					'立即开始，探索无限可能'
				],
				indicatorStyle: 'width: 8rpx; height: 8rpx; border-radius: 4rpx;',
				indicatorActiveStyle: 'width: 16rpx; height: 8rpx; border-radius: 4rpx;'
			}
		},
		onLoad() {
			// 检查是否首次打开应用
			const isFirstOpen = !uni.getStorageSync('notFirstOpen');
			if (!isFirstOpen) {
				// 非首次打开，直接跳转到首页
				this.navigateToHome();
			}
		},
		methods: {
			// 处理轮播图改变事件
			handleSwiperChange(e) {
				this.currentIndex = e.detail.current;
			},
			// 下一页，带动画效果
			nextSlide() {
				if (this.currentIndex < this.guideImages.length - 1) {
					// 使用动画过渡到下一页
					this.currentIndex += 1;
				}
			},
			// 跳过引导页
			skipGuide() {
				// 标记已经不是首次打开
				uni.setStorageSync('notFirstOpen', true);
				this.navigateToHome();
			},
			// 导航到首页
			navigateToHome() {
				uni.switchTab({
					url: '/pages/investment/sygindex'
				});
			}
		}
	}
</script>

<style lang="scss">
	.guide-container {
		position: relative;
		width: 100%;
		height: 100vh;
		overflow: hidden;
	}
	
	.blur-background {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(255,170,0,0.2), #ffaa7f, #ffff7f);
		filter: blur(10rpx); /* 实际模糊效果通过伪元素实现 */
		opacity: 0.85;
		z-index: 1;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			// background: url('/static/image/yd4.png') center center no-repeat;
			background-size: 200% 200%;
			filter: blur(40rpx);
			opacity: 0.3;
			transform: scale(1.5);
			z-index: -1;
		}
	}
	
	.swiper {
		width: 100%;
		height: 100%;
		z-index: 2;
		position: relative;
	}
	
	.swiper-item {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	
	.content-wrapper {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 0 30rpx;
	}
	
	.guide-image {
		width: 85%;
		// max-height: 90vh;
		object-fit: contain;
		margin-bottom: 30rpx;
	}
	
	.text-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-top: 20rpx;
	}
	
	.title {
		font-size: 48rpx;
		font-weight: bold;
		color: #FFFFFF;
		margin-bottom: 12rpx;
		text-align: center;
	}
	
	.desc {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.9);
		text-align: center;
		line-height: 1.5;
	}
	
	.skip-btn {
		position: absolute;
		top: 120rpx;
		right: 20rpx;
		padding: 6rpx 16rpx;
		background-color: rgba(255, 255, 255, 0.2);
		color: #ffaa00;
		font-size: 28rpx;
		border-radius: 20rpx;
		z-index: 10;
		transition: all 0.3s;
		backdrop-filter: blur(10rpx);
		-webkit-backdrop-filter: blur(10rpx);
		
		&:active {
			transform: scale(0.95);
			opacity: 0.8;
		}
	}
	
	.next-btn, .start-btn {
		position: absolute;
		bottom: 120rpx;
		left: 50%;
		transform: translateX(-50%);
		padding: 12rpx 40rpx;
		background: rgba(255, 255, 255, 0.25);
		color: #ffaa00;
		font-size: 32rpx;
		font-weight: 500;
		border-radius: 30rpx;
		box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s;
		z-index: 5;
		backdrop-filter: blur(10rpx);
		-webkit-backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.3);
		
		&:active {
			transform: translateX(-50%) scale(0.95);
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		}
	}
	
	/* 动画过渡效果 */
	.fade-enter-active, .fade-leave-active {
		transition: opacity 0.5s;
	}
	.fade-enter, .fade-leave-to {
		opacity: 0;
	}
	uni-swiper .uni-swiper-dots-horizontal{
		bottom:60rpx !important;
		
	}
</style> 