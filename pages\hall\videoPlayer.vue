<template>
	<view class="video-player-container">
		<!-- 动态背景 -->
		<view class="background-layer">
			<view class="bg-gradient"></view>
			<view class="bg-particles">
				<view class="particle" v-for="n in 15" :key="n" :style="getParticleStyle(n)"></view>
			</view>
			<view class="bg-waves">
				<view class="wave wave1"></view>
				<view class="wave wave2"></view>
				<view class="wave wave3"></view>
			</view>
		</view>

		<!-- 返回按钮 -->
		<!-- <view class="back-button" @click="goBack">
			<view class="back-btn-bg">
				<u-icon name="arrow-left" size="20" color="#fff"></u-icon>
			</view>
		</view> -->

		<!-- 播放器容器 -->
		<view class="player-main">
			<view class="player-frame">
				<view
					id="player"
					class="player-container"
					:change:prop="renderPlayer.createPlayer"
					:prop="playerConfig"
				></view>

				<!-- 播放器装饰边框 -->
				<view class="player-border">
					<view class="border-corner tl"></view>
					<view class="border-corner tr"></view>
					<view class="border-corner bl"></view>
					<view class="border-corner br"></view>
				</view>
			</view>

			<!-- 播放器状态遮罩 -->
			<view v-if="loading || error" class="player-overlay">
				<!-- 加载状态 -->
				<view v-if="loading" class="loading-state">
					<view class="loading-ring">
						<view class="ring-segment" v-for="n in 8" :key="n" :style="getRingStyle(n)"></view>
					</view>
					<text class="loading-text">正在加载视频</text>
					<view class="loading-dots">
						<view class="dot" v-for="n in 3" :key="n" :style="getDotStyle(n)"></view>
					</view>
				</view>

				<!-- 错误状态 -->
				<view v-if="error" class="error-state">
					<view class="error-icon-wrapper">
						<view class="error-pulse"></view>
						<u-icon name="close-circle" size="60" color="#ff4757"></u-icon>
					</view>
					<text class="error-title">播放失败</text>
					<text class="error-message">{{ errorMessage }}</text>
					<view class="retry-button" @click="retryLoad">
						<view class="btn-shine"></view>
						<u-icon name="reload" size="16" color="#fff"></u-icon>
						<text class="retry-text">重新加载</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 视频标题（浮动显示） -->
		<view class="video-title-float" v-if="videoTitle && !loading && !error">
			<view class="title-bg">
				<text class="title-text">{{ videoTitle }}</text>
			</view>
	</view>
</view>
</template>

<!-- 阿里云播放器 renderjs -->
<script module="renderPlayer" lang="renderjs">
import Aliplayer from 'aliyun-aliplayer';
import "aliyun-aliplayer/build/skins/default/aliplayer-min.css";

export default {
	data() {
		return {
			player: null
		}
	},
	mounted() {
		console.log('renderjs mounted');
	},
	beforeDestroy() {
		this.destroyPlayer();
	},
	methods: {
		// 创建阿里云播放器
		createPlayer(newValue, oldValue, ownerInstance, instance) {
			console.log('创建阿里云播放器，配置:', newValue);
			
			if (!newValue || !newValue.vid || !newValue.playauth) {
				console.log('播放器配置不完整，跳过创建');
				return;
			}
			
			// 销毁现有播放器
			if (this.player) {
				this.destroyPlayer();
			}
			
			try {
				console.log('初始化阿里云播放器...');
				
				// 创建自定义CSS样式，增大控制按钮尺寸
				const customStyle = document.createElement('style');
				customStyle.textContent = `
					.prism-player .prism-big-play-btn {
						width: 70px !important;
						height: 70px !important;
						background-size: contain !important;
						z-index: 1100 !important;
					}
					.prism-player .prism-controlbar {
						height: 45px !important;
						line-height: 45px !important;
						z-index: 1100 !important;
					}
					.prism-player .prism-controlbar .prism-play-btn {
						width: 40px !important;
						height: 40px !important;
						margin-top: 3px !important;
						background-size: 20px !important;
						position: relative !important;
					}
					.prism-player .prism-controlbar .prism-fullscreen-btn {
						width: 40px !important;
						height: 40px !important;
						margin-top: 3px !important;
						background-size: 20px !important;
						position: relative !important;
					}
					.prism-player .prism-controlbar .prism-volume {
						width: 40px !important;
						height: 40px !important;
						margin-top: 3px !important;
						position: relative !important;
					}
					.prism-player .prism-controlbar .prism-volume .volume-icon {
						display:none !important;
						margin-top: 12px !important;
						background-size: 20px !important;
					}
					.prism-player .prism-controlbar .prism-time-display {
						font-size: 14px !important;
						line-height: 45px !important;
						height: 45px !important;
						position: relative !important;
						margin-left: 5px !important;
						margin-right: 5px !important;
					}
					.prism-player .prism-progress-cursor {
						width: 16px !important;
						height: 16px !important;
						margin-left: -8px !important;
						margin-top: -8px !important;
					}
					.prism-player .prism-controlbar .prism-progress {
						height: 3px !important;
						margin-top: 0 !important;
					}
					.prism-player video {
						object-fit: contain !important;
					}
				`;
				document.head.appendChild(customStyle);
				
				this.player = new Aliplayer({
					id: 'player',
					vid: newValue.vid,
					playauth: newValue.playauth,
					region: newValue.region || 'cn-shanghai',
					width: '100%',
					height: '100%',
					autoplay: false,
					isLive: false,
					rePlay: false,
					playsinline: true,
					preload: true,
					controlBarVisibility: 'always',
					useH5Prism: true,
					// 允许系统全屏
					x5_video: {
						// 启用同层播放器
						useH5Prism: true,
						// 允许全屏
						x5_type: 'h5',
						// 使用 H5 播放器的全屏方案
						x5_fullscreen: true,
						// 使用 H5 播放器的横屏方案
						x5_orientation: 'landscape'
					},
					// 设置全屏模式
					x5_orientation: 'landscape',
					// 启用屏幕旋转
					rotateFullscreen: true,
					// 启用自动旋转
					autoRotate: true,
					// 精简配置播放器皮肤
					skinLayout: [
						{
							name: "bigPlayButton",
							align: "cc"
						},
						{
							name:"H5Loading",
							align:"cc"
						},
						{
							name: "errorDisplay",
							align: "tlabs",
							x: 0,
							y: 0
						},
						{
							name: "infoDisplay"
						},
						{
							name: "controlBar",
							align: "blabs",
							x: 0,
							y: 0,
							children: [
								{
									name: "progress",
									align: "blabs",
									x: 0,
									y: 44
								},
								{
									name: "playButton",
									align: "tl",
									x: 15,
									y: 12
								},
								{
									name: "timeDisplay",
									align: "tl",
									x: 10,
									y: 12
								},
								{
									name: "fullScreenButton",
									align: "tr",
									x: 10,
									y: 12
								},
								{
									name: "volume",
									align: "tr",
									x: 5,
									y: 12
								}
							]
						}
					],
					// 添加License配置
					license: {
						domain: "***************", // 申请 License 时填写的域名
						key: "IPWsGiG4S01ssucgn8207ff3e34794cddbea8ce111a94c7b8" // 申请成功后，在控制台可以看到 License Key
					}
				});
				
				// 播放器事件监听
				this.player.on('ready', () => {
					console.log('阿里云播放器准备就绪');
					ownerInstance.callMethod('onPlayerReady');
					
					// 保存播放器实例到Vue组件
					ownerInstance.callMethod('setPlayerInstance', {
						hasPlayer: true
					});
				});
				
				this.player.on('play', () => {
					console.log('开始播放');
					ownerInstance.callMethod('onPlayerPlay');
				});
				
				this.player.on('pause', () => {
					console.log('暂停播放');
					ownerInstance.callMethod('onPlayerPause');
				});
				
				this.player.on('ended', () => {
					console.log('播放结束');
					ownerInstance.callMethod('onPlayerEnded');
				});
				
				this.player.on('error', (err) => {
					console.error('播放器错误:', err);
					
					// 提取错误信息，避免传递复杂对象
					let errorInfo = {
						type: 'error',
						message: '播放失败'
					};
					
					if (err && err.paramData) {
						errorInfo.paramData = {
							error_code: err.paramData.error_code,
							error_msg: err.paramData.error_msg,
							display_msg: err.paramData.display_msg
						};
					}
					
					ownerInstance.callMethod('onPlayerError', errorInfo);
				});

				// 监听全屏事件
				this.player.on('requestFullScreen', () => {
					console.log('进入全屏模式');
					ownerInstance.callMethod('onFullScreenChange', { isFullScreen: true });
					
					// 通知父窗口进入全屏
					try {
						window.parent.postMessage({
							type: 'fullscreen',
							from: 'videoPlayer',
							data: { isFullScreen: true }
						}, '*');
					} catch (e) {
						console.error('发送全屏消息失败:', e);
					}
				});

				this.player.on('cancelFullScreen', () => {
					console.log('退出全屏模式');
					ownerInstance.callMethod('onFullScreenChange', { isFullScreen: false });
					
					// 通知父窗口退出全屏
					try {
						window.parent.postMessage({
							type: 'fullscreen',
							from: 'videoPlayer',
							data: { isFullScreen: false }
						}, '*');
					} catch (e) {
						console.error('发送退出全屏消息失败:', e);
					}
				});

				// 监听来自父窗口的消息
				window.addEventListener('app-message', (event) => {
					console.log('收到父窗口消息:', event.detail);
					
					if (event.detail && event.detail.type) {
						switch (event.detail.type) {
							case 'fullscreenChange':
								// 父窗口通知全屏状态变化
								ownerInstance.callMethod('onFullScreenChange', { 
									isFullScreen: event.detail.data.isFullScreen 
								});
								break;
							case 'requestFullscreen':
								// 父窗口请求进入全屏
								if (this.player && !this.player.fullscreenService.isFullScreen()) {
									this.player.fullscreenService.requestFullScreen();
								}
								break;
							case 'exitFullscreen':
								// 父窗口请求退出全屏
								if (this.player && this.player.fullscreenService.isFullScreen()) {
									this.player.fullscreenService.cancelFullScreen();
								}
								break;
						}
					}
				});
				
			} catch (error) {
				console.error('创建阿里云播放器失败:', error);
				ownerInstance.callMethod('onPlayerError', { type: 'error', message: error.message || '播放器初始化失败' });
			}
		},
		
		// 销毁播放器
		destroyPlayer() {
			if (this.player) {
				try {
					this.player.dispose();
					this.player = null;
					console.log('阿里云播放器已销毁');
				} catch (error) {
					console.error('销毁播放器失败:', error);
				}
			}
		},

		// 处理屏幕方向变化
		handleOrientationChange() {
			console.log('屏幕方向变化:', window.orientation);
		},

		// 尝试替代全屏方法
		tryAlternativeFullscreen() {
			console.log('尝试替代全屏方法');
			
			// 尝试使用HTML5全屏API
			const playerElement = document.getElementById('player');
			if (playerElement) {
				if (playerElement.requestFullscreen) {
					playerElement.requestFullscreen();
				} else if (playerElement.webkitRequestFullscreen) {
					playerElement.webkitRequestFullscreen();
				} else if (playerElement.mozRequestFullScreen) {
					playerElement.mozRequestFullScreen();
				} else if (playerElement.msRequestFullscreen) {
					playerElement.msRequestFullscreen();
				}
			}
			
			// 尝试设置视口
			try {
				const metaViewport = document.querySelector('meta[name=viewport]');
				if (metaViewport) {
					metaViewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
				} else {
					const meta = document.createElement('meta');
					meta.name = 'viewport';
					meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
					document.head.appendChild(meta);
				}
			} catch (e) {
				console.error('设置视口失败:', e);
			}
		}
	}
}
</script>

<script>
export default {
	data() {
		return {
			// 页面参数
			chapterId: '',
			videoTitle: '视频播放',
			videoDescription: '',

			// 播放授权信息（从详情页面传递过来）
			videoId: '',
			playAuth: '',

			// 播放器状态
			loading: true,
			error: false,
			errorMessage: '',
			isPlaying: false,
			isFullScreen: false,

			// 视频信息
			videoDuration: 0,
			videoHeat: 0,
			videoPoster: '',
			playCount: 0,
			videoStatus: '',

			// 播放器配置（传递给renderjs）
			playerConfig: null,
			
			// 播放器实例引用
			hasPlayer: false
		}
	},
	
	onLoad(options) {
		console.log('视频播放页面参数:', options);

		// 解锁屏幕方向，允许自由旋转
		// #ifdef APP-PLUS
		try {
			plus.screen.unlockOrientation();
			console.log('已解锁屏幕方向，允许自由旋转');
		} catch (e) {
			console.error('解锁屏幕方向失败:', e);
		}
		// #endif

		// 获取基本信息
		if (options.chapterId) {
			this.chapterId = options.chapterId;
		}

		if (options.title) {
			this.videoTitle = decodeURIComponent(options.title);
		}

		// 获取播放授权信息（从详情页面传递过来）
		if (options.videoId) {
			this.videoId = options.videoId;
		}

		if (options.playAuth) {
			// 解码PlayAuth，防止URL编码问题
			this.playAuth = decodeURIComponent(options.playAuth);
			console.log('解码后的PlayAuth长度:', this.playAuth.length);
		}

		// 获取视频元数据
		if (options.duration) {
			this.videoDuration = parseInt(options.duration);
		}

		if (options.heat) {
			this.videoHeat = parseInt(options.heat);
		}

		if (options.poster) {
			this.videoPoster = decodeURIComponent(options.poster);
		}

		if (options.status) {
			this.videoStatus = options.status;
		}

		if (options.playCount) {
			this.playCount = parseInt(options.playCount);
		}

		// 验证必要参数
		if (!this.videoId || !this.playAuth) {
			this.showError('播放参数不完整，请重新进入');
			return;
		}

		// 直接初始化播放器
		this.initAliyunPlayer();
	},
	
	methods: {
		// 初始化阿里云播放器
		initAliyunPlayer() {
			console.log('初始化播放器，videoId:', this.videoId, 'playAuth:', this.playAuth ? '已获取' : '未获取');

			// 设置播放器配置，触发renderjs创建播放器
			this.$nextTick(() => {
				this.playerConfig = {
					vid: this.videoId,
					playauth: this.playAuth,
					region: 'cn-shanghai'
				};

				console.log('设置播放器配置:', this.playerConfig);
			});
		},
		
		// 播放器事件回调（由renderjs调用）
		onPlayerReady() {
			console.log('播放器准备完成');
			this.loading = false;
			this.error = false;
		},
		
		onPlayerPlay() {
			console.log('开始播放');
			this.isPlaying = true;
		},

		onPlayerPause() {
			console.log('暂停播放');
			this.isPlaying = false;
		},

		onPlayerEnded() {
			console.log('播放结束');
			this.isPlaying = false;
		},
		
		onPlayerError(error) {
			console.error('播放器错误:', error);
			
			// 处理License错误
			if (error && error.paramData) {
				const errorData = error.paramData;
				if (errorData.error_code === 4035) {
					this.showError('播放器License验证失败，请联系管理员');
				} else if (errorData.error_msg) {
					this.showError('播放失败：' + errorData.error_msg);
				} else {
					this.showError('视频播放失败，请重试');
				}
			} else {
				this.showError('视频播放失败，请重试');
			}
		},
		
		// 显示错误
		showError(message) {
			this.loading = false;
			this.error = true;
			this.errorMessage = message;
		},
		
		// 重试加载
		retryLoad() {
			if (!this.videoId || !this.playAuth) {
				this.showError('播放参数不完整，请返回重新进入');
				return;
			}

			this.loading = true;
			this.error = false;
			this.initAliyunPlayer();
		},
		
		// 设置播放器实例引用
		setPlayerInstance(data) {
			console.log('设置播放器实例引用:', data);
			this.hasPlayer = data.hasPlayer;
		},

		// 全屏状态变化
		onFullScreenChange(data) {
			console.log('全屏状态变化:', data);
			this.isFullScreen = data.isFullScreen;
		},

		// 请求全屏并旋转屏幕
		requestFullScreen() {
			console.log('请求全屏并旋转屏幕');
			this.isFullScreen = true;
			
			// #ifdef APP-PLUS
			try {
				// 获取当前方向
				const currentOrientation = plus.navigator.getOrientation();
				console.log('当前屏幕方向:', currentOrientation);
				
				// 解除方向锁定，允许自由旋转
				plus.screen.unlockOrientation();
				
				// 设置全屏
				const currentWebview = plus.webview.currentWebview();
				currentWebview.setStyle({
					'popGesture': 'none',
					'bounce': 'none',
					'scrollIndicator': 'none',
					'scalable': false
				});
				
				// 隐藏系统状态栏
				plus.navigator.setFullscreen(true);
				
				console.log('设置全屏成功');
			} catch (e) {
				console.error('设置全屏失败:', e);
			}
			// #endif
			
			// #ifndef APP-PLUS
			try {
				// 尝试使用HTML5全屏API
				const video = document.querySelector('#player video');
				if (video) {
					if (video.requestFullscreen) {
						video.requestFullscreen();
					} else if (video.webkitRequestFullscreen) {
						video.webkitRequestFullscreen();
					} else if (video.mozRequestFullScreen) {
						video.mozRequestFullScreen();
					} else if (video.msRequestFullscreen) {
						video.msRequestFullscreen();
					}
				}
				
				// 尝试锁定屏幕方向
				if (window.screen && window.screen.orientation) {
					window.screen.orientation.lock('landscape').catch(e => {
						console.error('无法锁定屏幕方向:', e);
					});
				}
			} catch (e) {
				console.error('设置HTML5全屏失败:', e);
			}
			// #endif
		},

		// 退出全屏
		exitFullScreen() {
			console.log('退出全屏');
			this.isFullScreen = false;
			
			// #ifdef APP-PLUS
			try {
				// 退出全屏
				plus.navigator.setFullscreen(false);
				
				// 解除方向锁定，允许自由旋转
				plus.screen.unlockOrientation();
				
				console.log('退出全屏成功');
			} catch (e) {
				console.error('退出全屏失败:', e);
			}
			// #endif
			
			// #ifndef APP-PLUS
			try {
				// 尝试使用HTML5退出全屏API
				if (document.exitFullscreen) {
					document.exitFullscreen();
				} else if (document.webkitExitFullscreen) {
					document.webkitExitFullscreen();
				} else if (document.mozCancelFullScreen) {
					document.mozCancelFullScreen();
				} else if (document.msExitFullscreen) {
					document.msExitFullscreen();
				}
				
				// 解锁屏幕方向
				if (window.screen && window.screen.orientation) {
					window.screen.orientation.unlock();
				}
			} catch (e) {
				console.error('退出HTML5全屏失败:', e);
			}
			// #endif
		},

		// 返回上一页
		goBack() {
			console.log('返回按钮点击, 全屏状态:', this.isFullScreen);
			
			// 如果处于全屏状态，先退出全屏
			if (this.isFullScreen) {
				this.exitFullScreen();
				
				// 如果是APP环境，还需要调用播放器的退出全屏方法
				// #ifdef APP-PLUS
				try {
					const playerEl = document.getElementById('player');
					if (playerEl && playerEl.__vue__ && playerEl.__vue__.player) {
						playerEl.__vue__.player.cancelFullScreen();
					}
				} catch (e) {
					console.error('退出播放器全屏失败:', e);
				}
				// #endif
				
				return; // 先退出全屏，不直接返回
			}
			
			// 发送消息给父窗口
			try {
				// 使用postMessage
				window.parent.postMessage({
					type: 'close',
					from: 'videoPlayer'
				}, '*');
				console.log('已发送关闭消息到父窗口(postMessage)');
			} catch (e) {
				console.error('发送消息失败:', e);
			}
			
			// 简化返回逻辑，避免多重尝试导致的刷新问题
			try {
				// 如果在H5环境中，尝试使用history返回
				window.history.back();
			} catch (e) {
				console.log('返回失败:', e);
			}
		},
		
		// 获取视频状态
		getVideoStatus() {
			return this.videoStatus || '正常';
		},

		// 获取视频时长
		getVideoDuration() {
			return this.videoDuration || 0;
		},

		// 格式化时长
		formatDuration(seconds) {
			if (!seconds) return '0分钟';

			const minutes = Math.floor(seconds / 60);
			const hours = Math.floor(minutes / 60);

			if (hours > 0) {
				return hours + '小时' + (minutes % 60) + '分钟';
			}
			return minutes + '分钟';
		},

		// 格式化热度
		formatHeat(heat) {
			if (!heat) return '0';
			if (heat >= 10000) {
				return (heat / 10000).toFixed(1) + 'w';
			}
			if (heat >= 1000) {
				return (heat / 1000).toFixed(1) + 'k';
			}
			return heat.toString();
		},

		// 获取粒子样式
		getParticleStyle(index) {
			const delay = Math.random() * 3;
			const duration = 3 + Math.random() * 2;
			const size = 2 + Math.random() * 4;
			const left = Math.random() * 100;
			const opacity = 0.3 + Math.random() * 0.4;

			return {
				left: left + '%',
				width: size + 'px',
				height: size + 'px',
				opacity: opacity,
				animationDelay: delay + 's',
				animationDuration: duration + 's'
			};
		},

		// 获取加载环样式
		getRingStyle(index) {
			const delay = index * 0.1;
			return {
				animationDelay: delay + 's'
			};
		},

		// 获取加载点样式
		getDotStyle(index) {
			const delay = index * 0.2;
			return {
				animationDelay: delay + 's'
			};
		},


	}
}
</script>

<style lang="scss" scoped>
.video-player-container {
	position: relative;
	width: 100vw;
	height: 100vh;
	overflow: hidden;
}

/* 动态背景层 */
.background-layer {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1; /* 背景层级最低 */

	.bg-gradient {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg,
			#667eea 0%,
			#764ba2 25%,
			#f093fb 50%,
			#f5576c 75%,
			#4facfe 100%);
		animation: gradientShift 10s ease-in-out infinite;
	}

	.bg-particles {
		position: absolute;
		top: 200px;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1; /* 确保在背景层但不会跑到最上面 */

		.particle {
			position: absolute;
			background: rgba(255, 255, 255, 0.6);
			border-radius: 50%;
			animation: particleFloat 5s ease-in-out infinite;
		}
	}

	.bg-waves {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 200rpx;
		overflow: hidden;

		.wave {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 200%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
			border-radius: 50% 50% 0 0;
			animation: waveMove 8s ease-in-out infinite;

			&.wave1 {
				animation-delay: 0s;
				opacity: 0.3;
			}

			&.wave2 {
				animation-delay: -2s;
				opacity: 0.2;
			}

			&.wave3 {
				animation-delay: -4s;
				opacity: 0.1;
			}
		}
	}
}

/* 播放器主容器 */
.player-main {
	position: absolute;
	top: 50%; /* 居中放置 */
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 100; 
	width: 95%;
	max-width: 900rpx;

	.player-frame {
		position: relative;
		width: 100%;
		aspect-ratio: 16/9; /* 保持16:9比例 */
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);

		.player-container {
			width: 100%;
			height: 100%;
			background: #000;
			border-radius: 20rpx;
			position: relative;
			overflow: hidden;
		}

		.player-border {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			pointer-events: none;

			.border-corner {
				position: absolute;
				width: 50rpx; /* 增大装饰边框 */
				height: 50rpx;
				border: 6rpx solid rgba(255, 255, 255, 0.8); /* 增强边框和透明度 */
				animation: borderGlow 3s ease-in-out infinite; /* 添加发光动画 */

				&.tl {
					top: 20rpx;
					left: 20rpx;
					border-right: none;
					border-bottom: none;
					border-radius: 10rpx 0 0 0;
				}

				&.tr {
					top: 20rpx;
					right: 20rpx;
					border-left: none;
					border-bottom: none;
					border-radius: 0 10rpx 0 0;
				}

				&.bl {
					bottom: 20rpx;
					left: 20rpx;
					border-right: none;
					border-top: none;
					border-radius: 0 0 0 10rpx;
				}

				&.br {
					bottom: 20rpx;
					right: 20rpx;
					border-left: none;
					border-top: none;
					border-radius: 0 0 10rpx 0;
				}
			}
		}
	}
}

/* 播放器遮罩 */
.player-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 200;
	border-radius: 20rpx;

	.loading-state {
		display: flex;
		flex-direction: column;
		align-items: center;

		.loading-ring {
			position: relative;
			width: 80rpx;
			height: 80rpx;
			margin-bottom: 30rpx;

			.ring-segment {
				position: absolute;
				width: 100%;
				height: 100%;
				border: 4rpx solid transparent;
				border-top-color: #ff6a00;
				border-radius: 50%;
				animation: ringRotate 1.2s linear infinite;
			}
		}

		.loading-text {
			color: #fff;
			font-size: 28rpx;
			font-weight: 500;
			margin-bottom: 20rpx;
		}

		.loading-dots {
			display: flex;
			gap: 8rpx;

			.dot {
				width: 8rpx;
				height: 8rpx;
				background: #ff6a00;
				border-radius: 50%;
				animation: dotBounce 1.4s ease-in-out infinite;
			}
		}
	}

	.error-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;

		.error-icon-wrapper {
			position: relative;
			margin-bottom: 30rpx;

			.error-pulse {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 120rpx;
				height: 120rpx;
				background: rgba(255, 71, 87, 0.2);
				border-radius: 50%;
				animation: errorPulse 2s ease-in-out infinite;
			}
		}

		.error-title {
			color: #fff;
			font-size: 32rpx;
			font-weight: 600;
			margin-bottom: 15rpx;
		}

		.error-message {
			color: rgba(255, 255, 255, 0.8);
			font-size: 26rpx;
			margin-bottom: 40rpx;
			max-width: 400rpx;
			line-height: 1.5;
		}

		.retry-button {
			position: relative;
			display: flex;
			align-items: center;
			background: linear-gradient(135deg, #ff6a00 0%, #ff8533 100%);
			padding: 20rpx 40rpx;
			border-radius: 30rpx;
			overflow: hidden;

			.btn-shine {
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
				animation: btnShine 2s ease-in-out infinite;
			}

			.retry-text {
				color: #fff;
				font-size: 28rpx;
				font-weight: 600;
				margin-left: 10rpx;
			}
		}
	}
}



/* 浮动标题 */
.video-title-float {
	position: fixed;
	bottom: 50rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 500;
	max-width: 85%;

	.title-bg {
		background: rgba(0, 0, 0, 0.7);
		backdrop-filter: blur(20rpx);
		padding: 20rpx 30rpx;
		border-radius: 30rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.2);

		.title-text {
			color: #fff;
			font-size: 28rpx;
			font-weight: 600;
			text-align: center;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
		}
	}
}

/* 动画定义 */
@keyframes gradientShift {
	0%, 100% { filter: hue-rotate(0deg); }
	50% { filter: hue-rotate(180deg); }
}

@keyframes particleFloat {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes waveMove {
	0% { transform: translateX(-50%) rotate(0deg); }
	100% { transform: translateX(-50%) rotate(360deg); }
}

@keyframes ringRotate {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes dotBounce {
	0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
	40% { transform: scale(1.2); opacity: 1; }
}

@keyframes errorPulse {
	0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
	50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.1; }
}

@keyframes btnShine {
	0% { left: -100%; }
	100% { left: 100%; }
}

@keyframes borderGlow {
	0%, 100% {
		border-color: rgba(255, 255, 255, 0.8);
		box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.3);
	}
	50% {
		border-color: rgba(255, 106, 0, 0.9);
		box-shadow: 0 0 20rpx rgba(255, 106, 0, 0.5);
	}
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.player-main {
		width: 98%; /* 小屏幕下更大宽度 */
		top: 45%; /* 调整垂直位置 */

		.player-frame {
			aspect-ratio: 16/9; /* 保持16:9比例 */
		}
	}

	.back-button {
		top: 30rpx; /* 小屏幕下调整位置 */
		left: 30rpx;

		.back-btn-bg {
			width: 70rpx;
			height: 70rpx;
		}
	}

	.video-title-float {
		bottom: 60rpx; /* 小屏幕下调整位置 */
		max-width: 90%;

		.title-bg {
			padding: 15rpx 25rpx;

			.title-text {
				font-size: 26rpx;
			}
		}
	}
}

/* 返回按钮 */
.back-button {
	position: fixed;
	top: 40rpx; /* 调整顶部距离，避开状态栏 */
	left: 30rpx;
	z-index: 1000; /* 确保在最上层 */

	.back-btn-bg {
		width: 80rpx;
		height: 80rpx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		backdrop-filter: blur(20rpx);
		border: 2rpx solid rgba(255, 255, 255, 0.2);
		transition: all 0.3s ease;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3); /* 添加阴影增强可见性 */

		&:active {
			transform: scale(0.9);
			background: rgba(0, 0, 0, 0.7);
		}
	}
}

/* 使用深度选择器覆盖阿里云播放器样式 */
:deep(.prism-player) {
	.prism-big-play-btn {
		width: 70px !important;
		height: 70px !important;
		background-size: contain !important;
		z-index: 1100 !important;
	}
	
	.prism-controlbar {
		height: 45px !important;
		line-height: 45px !important;
		z-index: 1100 !important;
		
		.prism-play-btn {
			width: 40px !important;
			height: 40px !important;
			margin-top: 3px !important;
			background-size: 20px !important;
		}
		
		.prism-fullscreen-btn {
			width: 40px !important;
			height: 40px !important;
			margin-top: 3px !important;
			background-size: 20px !important;
		}
		
		.prism-volume {
			width: 40px !important;
			height: 40px !important;
			margin-top: 3px !important;
			
			.volume-icon {
				margin-top: 12px !important;
				background-size: 20px !important;
			}
		}
		
		.prism-time-display {
			font-size: 14px !important;
			line-height: 45px !important;
			height: 45px !important;
			margin-left: 5px !important;
			margin-right: 5px !important;
		}
		
		.prism-progress {
			height: 3px !important;
			margin-top: 0 !important;
		}
	}
	
	.prism-progress-cursor {
		width: 16px !important;
		height: 16px !important;
		margin-left: -8px !important;
		margin-top: -8px !important;
	}
	
	video {
		object-fit: contain !important;
	}
}
</style>
