<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header glass-header">
			<view class="header-content">
				<text class="header-title" v-if="!editMode">我的项目</text>
				<text class="header-title edit-title" v-else>编辑项目</text>
				<view class="header-actions">
					<view v-if="!editMode&&projectList.length>1" class="action-btn switch-btn" @click="openSwitchProjectModal">
						<u-icon name="list-dot" size="20"></u-icon>
						<text>切换</text>
					</view>
					<view v-if="!editMode" class="action-btn add-btn" @click="openInviteCodeModal">
						<u-icon name="plus" color="#fff" size="20"></u-icon>
						<text>添加</text>
					</view>
					<view v-else class="action-btn done-btn" @click="exitEditMode">
						<u-icon name="checkmark" color="#fff" size="20"></u-icon>
						<text>完成</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="content">
			<!-- 长按编辑提示 -->
			<view class="edit-hint" v-if="projectList.length > 0 && !editMode">
				<u-icon name="info-circle" size="14" color="#999"></u-icon>
				<text class="hint-text">长按项目可进入编辑模式</text>
			</view>

			<!-- 项目列表 -->
			<view class="project-list">
				<view
					class="project-item"
					:class="{'edit-mode': editMode, 'shake': editMode}"
					v-for="(item, index) in projectList"
					:key="index"
					@click="selectProject(item)"
					@longpress="enterEditMode"
				>
					<view class="project-card-inner">
						<!-- 删除按钮 -->
						<view
							v-if="editMode && item.selected !== 1"
							class="delete-btn"
							@click.stop="confirmDeleteProject(item)"
						>
							<u-icon name="close" size="16" color="#ffffff"></u-icon>
						</view>

						<!-- 不可删除提示 -->
						<view
							v-if="editMode && item.selected === 1"
							class="protected-badge"
						>
							<u-icon name="lock" size="14" color="#ffffff"></u-icon>
						</view>

						<view class="project-logo-wrapper">
							<view class="project-logo-bg"></view>
							<view class="project-logo-container">
								<image :src="item.logoImgUrl" mode="aspectFit" class="logo-image"></image>
							</view>
							<view class="project-status-badge" v-if="item.selected === 1">当前项目</view>
							<view class="project-status-icon" v-else>可切换</view>
						</view>
						<view class="project-info">
							<text class="project-title">{{item.companyName}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="projectList.length === 0 && !loading">
				<image src="/static/empty/2.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无项目</text>
				<view class="add-project-btn" @click="openInviteCodeModal">
					<u-icon name="plus" size="16" color="#ffffff"></u-icon>
					<text>添加新项目</text>
				</view>
			</view>
			
			<!-- 加载状态 -->
			<view class="loading-state" v-if="loading">
				<u-loading-icon mode="circle " size="36" color="#0055ff"></u-loading-icon>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
		
		<!-- 切换项目弹窗 -->
		<u-popup :show="showSwitchModal" mode="bottom" :safeAreaInsetBottom='false' border-radius="24" @close="showSwitchModal = false">
			<view class="switch-modal">
				<view class="modal-header">
					<text class="modal-title">切换项目</text>
					<view class="modal-close" @click="showSwitchModal = false">
						<u-icon name="close" size="20"></u-icon>
					</view>
				</view>
				<view class="modal-content">
					<scroll-view scroll-y style="max-height: 600rpx;">
						<view 
							class="switch-item" 
							v-for="(item, index) in projectList" 
							:key="index"
							:class="{'active': item.selected === 1}"
							@click="confirmSwitchProject(item)"
						>
							<view class="switch-item-logo-container">
								<image :src="item.logoImgUrl || '/static/images/default-project.png'" class="switch-item-image"></image>
							</view>
							<text class="switch-item-name">{{item.companyName}}</text>
							<u-icon v-if="item.selected === 1" name="checkmark-circle" color="#0055ff" size="18"></u-icon>
						</view>
					</scroll-view>
				</view>
			</view>
		</u-popup>

		<!-- 邀请码输入弹窗 -->
		<u-popup :safeAreaInsetBottom='false' :show="showInviteCodeModal" mode="center" border-radius="24" @close="closeInviteCodeModal">
			<view class="invite-code-modal">
				<view class="invite-modal-header">
					<view class="invite-modal-icon">
						<u-icon name="plus-square-fill" size="32" color="#0055ff"></u-icon>
					</view>
					<text class="invite-modal-title">输入邀请码</text>
					<text class="invite-modal-subtitle">通过邀请码快速加入项目</text>
				</view>

				<view class="invite-modal-content">
					<view class="invite-input-container" :class="{'shake': inputShake}">
						<u-input
							v-model="inviteCode"
							placeholder="请输入项目邀请码"
							border="none"
							:customStyle="inputStyle"
							class="invite-input"
							@focus="onInputFocus"
							@blur="onInputBlur"
							:focus="inputFocused"
						></u-input>
						<view class="input-border" :class="{'focused': inputFocused, 'error': inputError}"></view>
					</view>

					<view class="invite-modal-buttons">
						<view class="invite-btn-cancel" @click="closeInviteCodeModal">
							<text>取消</text>
						</view>
						<view class="invite-btn-confirm" @click="confirmInviteCode" :class="{'loading': submittingInvite}">
							<u-loading v-if="submittingInvite" mode="flower" size="20" color="#ffffff"></u-loading>
							<text v-else>确认加入</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>

		<u-toast ref="uToast"></u-toast>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loading: true,
			projectList: [],
			showSwitchModal: false, // 控制切换项目弹窗
			showInviteCodeModal: false, // 控制邀请码弹窗
			editMode: false, // 编辑模式
			currentPage: 1,
			pageSize: 10,
			hasMore: true,
			selectedProject: null, // 当前选中准备切换的项目
			inviteCode: '', // 邀请码
			submittingInvite: false, // 提交邀请码loading状态
			inputFocused: false, // 输入框焦点状态
			inputError: false, // 输入框错误状态
			inputShake: false, // 输入框震动动画
			inputStyle: {
				padding: '24rpx 0',
				fontSize: '32rpx',
				color: '#333333',
				backgroundColor: 'transparent'
			}
		}
	},
	onLoad() {
		// 获取项目列表
		this.getProjectList();
	},
	methods: {
		// 获取项目列表
		getProjectList(isAfterAdd = false) {
			this.loading = true;

			this.syghttp.ajax({
				url: this.syghttp.api.selectMyFocusCompany,
				method: 'GET',
				success: (res) => {
					console.log('selectMyFocusCompany API返回数据:', res);
					this.loading = false;

					if (res.code === 1000 && res.data && res.data.items && res.data.items.items) {
						// 首次加载，直接赋值
						this.projectList = res.data.items.items;

						// 判断是否还有更多数据
						if (res.data.items.totalCount > this.projectList.length) {
							this.hasMore = true;
						} else {
							this.hasMore = false;
						}

						// 如果是添加项目后的刷新，或者只有一个项目，需要更新缓存
						if (isAfterAdd || this.projectList.length === 1) {
							// 找到被选中的项目
							const selectedProject = this.projectList.find(item => item.selected === 1);
							if (selectedProject) {
								// 更新全局的defaultCompany
								const defaultCompany = {
									companyId: selectedProject.companyId,
									companyName: selectedProject.companyName,
									logoImgUrl: selectedProject.logoImgUrl
								};

								// 存储到本地缓存
								uni.setStorageSync('defaultCompany', defaultCompany);
								console.log('已更新defaultCompany:', defaultCompany);
							}
						}
					} else {
						this.projectList = [];
						this.hasMore = false;
					}
				},
				fail: (err) => {
					console.error('获取项目列表失败:', err);
					this.loading = false;

					// 显示错误提示
					this.$refs.uToast.show({
						title: '获取项目列表失败',
						type: 'error'
					});

					this.projectList = [];
				}
			});
		},
		
		// 选择项目
		selectProject(item) {
			// 编辑模式下禁用点击
			if (this.editMode) {
				return;
			}

			// 如果已经是当前项目，则直接跳转到主页
			if (item.selected === 1) {
				this.navigateToProjectDetail();
				return;
			}

			// 否则显示确认切换弹窗
			this.confirmSwitchProject(item);
		},
		
		// 导航到项目主页
		navigateToProjectDetail() {
			// 跳转到项目主页
			uni.switchTab({
				url: '/pages/investment/index'
			});
		},
		
		// 打开切换项目弹窗
		openSwitchProjectModal() {
			this.showSwitchModal = true;
		},
		
		// 确认切换项目
		confirmSwitchProject(item) {
			// 如果已经是当前项目，则不做操作
			if (item.selected === 1) {
				this.showSwitchModal = false;
				return;
			}
			
			this.selectedProject = item;
			this.showSwitchModal = false;
			
			// 使用uni.showModal替代u-modal
			uni.showModal({
				title: '切换到项目',
				content: `是否切换到项目"${item.companyName}"？`,
				confirmText: '确认切换',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.doSwitchProject();
					}
				}
			});
		},
		
		// 执行切换项目
		doSwitchProject() {
			if (!this.selectedProject) return;
			
			this.loading = true;
			
			// 调用接口切换项目
			this.syghttp.ajax({
				url: this.syghttp.api.selectDefaultCompany,
				method: 'POST',
				data: {
					id: this.selectedProject.focusId
				},
				success: (res) => {
					this.loading = false;
					
					if (res.code === 1000) {
						// 更新项目状态
						this.projectList.forEach(project => {
							project.selected = project.companyId === this.selectedProject.companyId ? 1 : 0;
						});
						
						// 更新全局的defaultCompany
						const defaultCompany = {
							companyId: this.selectedProject.companyId,
							companyName: this.selectedProject.companyName,
							logoImgUrl: this.selectedProject.logoImgUrl
						};
						
						// 存储到本地缓存
						uni.setStorageSync('defaultCompany', defaultCompany);
						
						// 显示成功提示
						this.$refs.uToast.show({
							message: '已切换到项目：' + this.selectedProject.companyName,
							type: 'success'
						});
						
						// 清除选中项
						this.selectedProject = null;
					} else {
						// 显示错误提示
						this.$refs.uToast.show({
							title: res.msg || '切换项目失败',
							type: 'error'
						});
					}
				},
				fail: (err) => {
					console.error('切换项目失败:', err);
					this.loading = false;
					
					// 显示错误提示
					this.$refs.uToast.show({
						title: '切换项目失败',
						type: 'error'
					});
				}
			});
		},
		
		// 跳转到添加项目页面
		navigateToAddProject() {
			uni.navigateTo({
				url: '/pages/investment/addProject'
			});
		},

		// 打开邀请码弹窗
		openInviteCodeModal() {
			this.showInviteCodeModal = true;
			this.inviteCode = '';
			this.inputError = false;
			this.inputShake = false;
		},

		// 关闭邀请码弹窗
		closeInviteCodeModal() {
			this.showInviteCodeModal = false;
			this.inviteCode = '';
			this.inputError = false;
			this.inputShake = false;
			this.inputFocused = false;
		},

		// 输入框获得焦点
		onInputFocus() {
			this.inputFocused = true;
			this.inputError = false;
		},

		// 输入框失去焦点
		onInputBlur() {
			this.inputFocused = false;
		},

		// 确认邀请码
		confirmInviteCode() {
			if (!this.inviteCode.trim()) {
				this.showInputError('请输入邀请码');
				return;
			}

			this.submittingInvite = true;

			// 调用createFocus接口
			this.syghttp.ajax({
				url: this.syghttp.api.createFocus,
				method: 'POST',
				data: {
					inviteCode: this.inviteCode.trim()
				},
				success: (res) => {
					this.submittingInvite = false;

					if (res.code === 1000) {
						// 成功提示
						this.$refs.uToast.show({
							message: '项目添加成功！',
							type: 'success'
						});

						// 关闭弹窗
						this.closeInviteCodeModal();

						// 刷新项目列表并获取新添加的项目信息
						setTimeout(() => {
							this.getProjectList(true);
						}, 500);
					} else {
						// 显示错误信息
						this.showInputError(res.msg || '邀请码无效，请检查后重试');
					}
				},
				fail: (err) => {
					console.error('添加项目失败:', err);
					this.submittingInvite = false;
					this.showInputError('网络错误，请稍后重试');
				}
			});
		},

		// 显示输入错误
		showInputError(message) {
			this.inputError = true;
			this.inputShake = true;

			// 显示错误提示
			this.$refs.uToast.show({
				title: message,
				type: 'error'
			});

			// 震动反馈
			if (uni.vibrateShort) {
				uni.vibrateShort();
			}

			// 清除震动动画
			setTimeout(() => {
				this.inputShake = false;
			}, 500);
		},

		// 进入编辑模式
		enterEditMode() {
			this.editMode = true;

			// 震动反馈
			if (uni.vibrateShort) {
				uni.vibrateShort();
			}
		},

		// 退出编辑模式
		exitEditMode() {
			this.editMode = false;
		},

		// 确认删除项目
		confirmDeleteProject(item) {
			// 当前项目不能删除
			if (item.selected === 1) {
				this.$refs.uToast.show({
					message: '当前项目不能删除',
					type: 'warning'
				});
				// return;
			}

			// 二次确认
			uni.showModal({
				title: '删除项目',
				content: `确定要删除项目"${item.companyName}"吗？删除后将无法恢复。`,
				confirmText: '删除',
				cancelText: '取消',
				confirmColor: '#ff4757',
				success: (res) => {
					if (res.confirm) {
						this.deleteProject(item);
					}
				}
			});
		},

		// 删除项目
		deleteProject(item) {
			this.loading = true;

			// 调用removeFocusCompany接口
			this.syghttp.ajax({
				url: this.syghttp.api.removeFocusCompany,
				method: 'POST',
				data: {
					id: item.focusId
				},
				success: (res) => {
					this.loading = false;

					if (res.code === 1000) {
						// 成功提示
						this.$refs.uToast.show({
							message: '项目删除成功',
							type: 'success'
						});

						// 从列表中移除
						const index = this.projectList.findIndex(project => project.focusId === item.focusId);
						if (index > -1) {
							this.projectList.splice(index, 1);
						}

						// 检查缓存中的项目信息
						try {
							const cachedCompany = uni.getStorageSync('defaultCompany');
							// 如果删除的是缓存中的项目，则清除缓存
							if (cachedCompany && cachedCompany.companyId === item.companyId) {
								// 如果还有其他项目，则更新为第一个项目
								if (this.projectList.length > 0) {
									// 找到被选中的项目（如果有）
									const selectedProject = this.projectList.find(project => project.selected === 1);
									if (selectedProject) {
										// 更新缓存为当前选中的项目
										const defaultCompany = {
											companyId: selectedProject.companyId,
											companyName: selectedProject.companyName,
											logoImgUrl: selectedProject.logoImgUrl
										};
										uni.setStorageSync('defaultCompany', defaultCompany);
									} else {
										// 如果没有选中的项目，则清除缓存
										uni.removeStorageSync('defaultCompany');
									}
								} else {
									// 如果没有项目了，则清除缓存
									uni.removeStorageSync('defaultCompany');
								}
							}
						} catch (e) {
							console.error('检查缓存项目信息失败:', e);
						}

						// 如果列表为空，退出编辑模式
						if (this.projectList.length === 0) {
							this.exitEditMode();
						}
					} else {
						// 显示错误信息
						this.$refs.uToast.show({
							title: res.msg || '删除失败，请稍后重试',
							type: 'error'
						});
					}
				},
				fail: (err) => {
					console.error('删除项目失败:', err);
					this.loading = false;

					// 显示错误提示
					this.$refs.uToast.show({
						title: '网络错误，请稍后重试',
						type: 'error'
					});
				}
			});
		}
	},
	
	// 下拉刷新
	onPullDownRefresh() {
		this.getProjectList();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
	padding-bottom: 30rpx;
}

.glass-header {
	backdrop-filter: blur(20rpx);
	background: rgba(255, 255, 255, 0.85);
}

.header {
	padding: 30rpx;
	box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.05);
	position: sticky;
	top: 0;
	z-index: 10;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.header-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333333;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

.header-actions {
	display: flex;
}

.action-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 70rpx;
	padding: 0 30rpx;
	margin-left: 20rpx;
	border-radius: 35rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	transition: all 0.2s;
}

.switch-btn {
	background-color: #f5f7fa;
	border: 1px solid rgba(0, 0, 0, 0.05);
}

.action-btn:active {
	transform: scale(0.95);
}

.action-btn text {
	font-size: 28rpx;
	margin-left: 8rpx;
}

.switch-btn text {
	color: #333333;
}

.invite-btn {
	background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
}

.invite-btn text {
	color: #ffffff;
}

.add-btn {
	background: linear-gradient(135deg, #0066ff 0%, #0099ff 100%);
}

.add-btn text {
	color: #ffffff;
}

.done-btn {
	background: linear-gradient(135deg, #2ed573 0%, #7bed9f 100%);
}

.done-btn text {
	color: #ffffff;
}

.edit-title {
	color: #ff6b35 !important;
}

.content {
	padding: 20rpx 30rpx;
}

.edit-hint {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16rpx 24rpx;
	margin-bottom: 20rpx;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 20rpx;
	backdrop-filter: blur(10rpx);

	.hint-text {
		font-size: 24rpx;
		color: #999;
		margin-left: 8rpx;
	}
}

.project-list {
	display: flex;
	flex-wrap: wrap;
	margin: 0 -10rpx;
}

.project-item {
	width: calc(50% - 20rpx);
	margin: 10rpx;
	border-radius: 24rpx;
	overflow: hidden;
	transition: all 0.3s;
	position: relative;
	transform-style: preserve-3d;
	perspective: 1000rpx;
}

.project-item:active {
	transform: translateY(4rpx);
}

.project-item.edit-mode {
	position: relative;
}

.project-item.shake {
	animation: shake-item 2s infinite;
}

@keyframes shake-item {
	0%, 100% { transform: translateX(0); }
	25% { transform: translateX(-2rpx); }
	75% { transform: translateX(2rpx); }
}

.project-card-inner {
	border-radius: 24rpx;
	background: rgba(255, 255, 255, 0.7);
	backdrop-filter: blur(10rpx);
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
	overflow: hidden;
	transition: all 0.3s;
	transform-style: preserve-3d;
	height: 100%;
}

.project-logo-wrapper {
	position: relative;
	height: 240rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	background: linear-gradient(145deg, #f0f4ff, #e6f0ff);
}

.project-logo-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(0, 102, 255, 0.1), rgba(0, 153, 255, 0.05));
	filter: blur(20rpx);
	transform: scale(1.1);
	z-index: 1;
}

.project-logo-container {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	border-radius: 24rpx;
	background: rgba(255, 255, 255, 0.9);
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15), 
				inset 0 -4rpx 10rpx rgba(0, 0, 0, 0.05),
				inset 0 4rpx 10rpx rgba(255, 255, 255, 0.8);
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	z-index: 2;
	transform: translateZ(20rpx);
}

.logo-image {
	width: 120rpx;
	height: 120rpx;
	object-fit: contain;
	transform: translateZ(10rpx);
	filter: drop-shadow(0 4rpx 6rpx rgba(0, 0, 0, 0.1));
}

.project-status-badge {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
	background: linear-gradient(135deg, #0066ff, #00a1ff);
	color: #ffffff;
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	box-shadow: 0 4rpx 10rpx rgba(0, 102, 255, 0.3);
	font-weight: 500;
	z-index: 3;
	transform: translateZ(30rpx);
}

.project-status-icon {
	position: absolute;
	top: 16rpx;
	right: 16rpx;
	background: rgba(0, 0, 0, 0.5);
	color: #ffffff;
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	z-index: 3;
	transform: translateZ(30rpx);
}

.project-info {
	padding: 24rpx 20rpx;
	background-color: #ffffff;
	position: relative;
	z-index: 2;
	transform: translateZ(5rpx);
}

.project-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333333;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	text-align: center;
}

/* 删除按钮样式 */
.delete-btn {
	position: absolute;
	top: 0rpx;
	left: 0rpx;
	width: 48rpx;
	height: 48rpx;
	background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4);
	border: 4rpx solid #ffffff;
	transition: all 0.2s ease;
}

.delete-btn:active {
	transform: scale(0.9);
	box-shadow: 0 2rpx 6rpx rgba(255, 71, 87, 0.3);
}

/* 保护标识样式 */
.protected-badge {
	position: absolute;
	top: 0rpx;
	left: 0rpx;
	width: 48rpx;
	height: 48rpx;
	background: linear-gradient(135deg, #ffa502 0%, #ff9f43 100%);
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10;
	box-shadow: 0 4rpx 12rpx rgba(255, 165, 2, 0.4);
	border: 4rpx solid #ffffff;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 150rpx 0;
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 30rpx;
	opacity: 0.8;
}

.empty-text {
	font-size: 30rpx;
	color: #999999;
	margin-bottom: 40rpx;
}

.add-project-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #0066ff 0%, #0099ff 100%);
	color: #ffffff;
	padding: 20rpx 40rpx;
	border-radius: 40rpx;
	font-size: 30rpx;
	box-shadow: 0 8rpx 20rpx rgba(0, 102, 255, 0.25);
	transition: all 0.2s;
}

.add-project-btn:active {
	transform: scale(0.95);
	box-shadow: 0 4rpx 10rpx rgba(0, 102, 255, 0.2);
}

.add-project-btn text {
	margin-left: 10rpx;
}

.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.loading-text {
	font-size: 28rpx;
	color: #999999;
	margin-top: 20rpx;
}

/* 切换项目弹窗样式 */
.switch-modal {
	background-color: #ffffff;
	padding: 40rpx 30rpx;
	border-radius: 24rpx 24rpx 0 0;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.modal-content {
	max-height: 600rpx;
}

.switch-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	border-radius: 16rpx;
	margin-bottom: 10rpx;
	transition: all 0.2s;
}

.switch-item.active {
	background-color: rgba(0, 102, 255, 0.08);
}

.switch-item:active {
	background-color: rgba(0, 102, 255, 0.12);
}

.switch-item-logo-container {
	width: 90rpx;
	height: 90rpx;
	border-radius: 16rpx;
	background: rgba(255, 255, 255, 0.9);
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
	margin-right: 24rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	overflow: hidden;
}

.switch-item-image {
	width: 70rpx;
	height: 70rpx;
	object-fit: contain;
}

.switch-item-name {
	flex: 1;
	font-size: 30rpx;
	color: #333333;
	font-weight: 500;
}

/* 邀请码弹窗样式 */
.invite-code-modal {
	width: 600rpx;
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.invite-modal-header {
	padding: 60rpx 40rpx 40rpx;
	text-align: center;
	background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
	position: relative;
}

.invite-modal-icon {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #0066ff 0%, #0099ff 100%);
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 30rpx;
	box-shadow: 0 10rpx 30rpx rgba(0, 102, 255, 0.3);
}

.invite-modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
	display: block;
	margin-bottom: 16rpx;
}

.invite-modal-subtitle {
	font-size: 28rpx;
	color: #666666;
	display: block;
}

.invite-modal-content {
	padding: 40rpx;
}

.invite-input-container {
	position: relative;
	margin-bottom: 50rpx;
	transition: all 0.3s ease;
}

.invite-input-container.shake {
	animation: shake 0.5s ease-in-out;
}

@keyframes shake {
	0%, 100% { transform: translateX(0); }
	25% { transform: translateX(-10rpx); }
	75% { transform: translateX(10rpx); }
}

.invite-input {
	width: 100%;
	background: #f8faff;
	border-radius: 16rpx;
	padding: 24rpx 20rpx !important;
	font-size: 32rpx;
	color: #333333;
	transition: all 0.3s ease;
}

.input-border {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	background: #e6f0ff;
	border-radius: 2rpx;
	transition: all 0.3s ease;
}

.input-border.focused {
	background: linear-gradient(135deg, #0066ff 0%, #0099ff 100%);
	box-shadow: 0 0 20rpx rgba(0, 102, 255, 0.3);
}

.input-border.error {
	background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
	box-shadow: 0 0 20rpx rgba(255, 71, 87, 0.3);
}

.invite-modal-buttons {
	display: flex;
	gap: 20rpx;
}

.invite-btn-cancel {
	flex: 1;
	height: 88rpx;
	background: #f5f7fa;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.invite-btn-cancel:active {
	transform: scale(0.95);
	background: #e8ecf0;
}

.invite-btn-cancel text {
	font-size: 32rpx;
	color: #666666;
	font-weight: 500;
}

.invite-btn-confirm {
	flex: 2;
	height: 88rpx;
	background: linear-gradient(135deg, #0066ff 0%, #0099ff 100%);
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(0, 102, 255, 0.3);
	transition: all 0.2s ease;
}

.invite-btn-confirm:active {
	transform: scale(0.95);
	box-shadow: 0 4rpx 12rpx rgba(0, 102, 255, 0.2);
}

.invite-btn-confirm.loading {
	background: linear-gradient(135deg, #99ccff 0%, #b3d9ff 100%);
	box-shadow: none;
}

.invite-btn-confirm text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: 600;
}
</style>