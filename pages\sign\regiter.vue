<template>
	<view class="page-box">
		<!-- 固定背景图片 -->
		<image
			src="/static/image/signBack.png"
			mode="widthFix"
			class="background-image"
		></image>

		<u-navbar :bgColor="bgColor" :placeholder='false' :auto-back="false" >
			<view class="u-nav-slot" slot="left">
				<u-icon name="arrow-left" size="24" color="#000" @click="back"></u-icon>
				<text class="com-fontsize-32"></text>
			</view>
		</u-navbar>
	
		<view style="flex-direction: column;padding-bottom: 120upx;" class="dis-ali jc_cen  ">
			<view class="content" style="align-self: flex-start;margin:254upx 0 140upx 100upx;font-size: 48rpx;">注册
			</view>
			<view>
				<u-input v-model="form.username" placeholder="输入您的手机号" prefixIcon="phone" suffixIcon="info-circle"
					height='50' style="width: 570upx;background-color: #f3f3f3;height: 120upx;"
					prefixIconStyle="font-size: 22px;color: #909399" fontSize='14px'>
					<template slot="prefix">
						<text class="com-fontsize-28 mr10">手机号</text><!-- <image src="../../static/image/sj.png" mode="widthFix" style="width: 30upx;margin-right: 15upx;">
						</image> -->
					</template>
					<template slot="suffix">
						<u-icon name="info-circle" size="17" :bold="true" style="margin-right: 10upx ;"
							@click="show=true"></u-icon>
					</template>
				</u-input>
			</view>
			<view >
				<u-input placeholder="输入验证码" prefixIcon="phone" suffixIcon="info-circle" height='50'
					style="width: 570upx;background-color: #f3f3f3;height: 120upx;"
					prefixIconStyle="font-size: 22px;color: #909399" fontSize='14px'>
					<template slot="prefix">
						<text class="com-fontsize-28 mr10">验证码</text>
						<!-- 	<image src="../../static/image/icons/yzm.png" mode="widthFix"
							style="width: 30upx;margin-right: 15upx;"></image> -->
					</template>
					<template slot="suffix">
						<view class="dis-ali">
							<!-- <u-icon name="info-circle" size="17" :bold="true" style="margin-right: 50upx ;"
								@click="show=true"></u-icon> -->
							<u-code ref="uCode" @change="codeChange" seconds="20" changeText="重新获取X秒"></u-code>
							<u-text @tap="getCode" :text="tips" color="#909399"></u-text>
						</view>

					</template>
				</u-input>
			</view>
			<view >
				<u-input v-model="form.password" placeholder="输入密码" type="password" prefixIcon="phone" height='50'
					style="width: 570upx;background-color: #f3f3f3;height: 120upx;" fontSize='14px'>
					<template slot="prefix">
						<text class="com-fontsize-28 mr25">密码</text>
						<!-- <image src="../../static/image/icons/mm.png" mode="widthFix"
							style="width: 30upx;margin-right: 15upx;"></image> -->
					</template>

				</u-input>
			</view>
			<view >
				<u-input v-model="form.promotion_code" placeholder="输入邀请码" type="text" prefixIcon="phone" height='50'
					style="width: 570upx;background-color: #f3f3f3;height: 120upx;" fontSize='14px'>
					<template slot="prefix">
						<text class="com-fontsize-28 mr10">邀请码</text>
						<!-- <image src="../../static/image/icons/mm.png" mode="widthFix"
							style="width: 30upx;margin-right: 15upx;"></image> -->
					</template>
			
				</u-input>
			</view>	
			<view class="dis-ali mb10" style="justify-content: flex-start;width: 570upx;margin-top: 20upx;">
				<u-checkbox-group iconPlacement='left' v-model="btn" placement="column" @change="ChangeIntegral">
					<u-checkbox name="1" shape='circle' size="18">
					</u-checkbox>
				</u-checkbox-group>
				<view style="font-size: 26upx;color: #939393;">
					我已阅读并同意<span style="color:  #f4ad32;" @click="goUrl(4)">用户服务协议，</span><span @click="goUrl(5)"
						style="color:  #f4ad32;">隐私政策</span>
				</view>
			</view>

			<view @click="register()" class="signBtn" style="">
				注册
			</view>
			<view @click="goUrl(1)" style="padding: 15upx 40upx;color: #F4AD32;border-radius: 50upx;font-size: 28upx;"
				class="dis-ali">
				<!-- <u-icon name="server-fill" color=" #f4ad32" style="margin-right: 10upx;"></u-icon> -->
				前去登录
			</view>
			<u-popup :show="show" mode="center" round='5'>
				<view style="padding: 40upx;width: 600upx;display: flex;align-items: center;flex-direction: column;">
					<text style="font-size: 34upx;font-weight: 700;">关于个人信息处理规则说明</text>
					<view style="line-height: 1.7;text-indent: 2em;margin-top: 20upx;font-size: 28upx;">
						同意/允许童彤乐向我推荐我可能感兴趣的内容，包括但不限于:会员权益服务、促销、活动、产品等信息。 如不希望接收上述信息，可联系客服处理</view>

					<view @click="show = false" class="showBtn" style="">
						我知道了
					</view>
				</view>
			</u-popup>

		</view>

	</view>
</template>

<script>
	var that
	export default {
		data() {
			return {
				list1: [
					'../../static/image/20230208195156793b17.png',
					'../../static/image/202302081952019c0ab4.png',
				],
				show: false,
				btn: [],
				show1: false,
				tips: '',
				value: '',
				form:{
					mobile:'',
					code:'',
					password:'',
					promotion_code:'',
					group:'app'
				},
				signWay:0,
				bgColor: "rgba(255,255,255,0.0)",
			}
		},
		watch: {
			value(newValue, oldValue) {
				// console.log('v-model', newValue);
			}
		},
		onLoad() {
			that = this;
		},
		methods: {
			back(){
					let canNavBack = getCurrentPages()
					console.log(canNavBack)
			  if( canNavBack && canNavBack.length>1) {  
			      uni.navigateBack() 
			  } else {  
			     uni.switchTab({
			     	url:'/pages/index/index'
			     })
			  }
			},
			register(){
				console.log(this.btn)
				if(this.btn.length!=0){
					this.http.ajax({
						url:that.http.api.register,
						method:'POST',
						data:that.form,
						success(res){ 
							if(res.code==200){
								uni.redirectTo({
									url:'/pages/sign/sign'
								})
								// uni.setStorageSync('token', res.data.access_token);
								// uni.navigateBack()
								// that.http.ajax({
								// 	url:that.http.api.info,
								// 	method:'POST',
								// 	success(res){ 
								// 		if(res.code==200){
								// 			uni.setStorageSync('info', res.data);
								// 		}else{
								// 			uni.showToast({
								// 				title:res.message,
								// 				icon:'none'
								// 			})
								// 		}
								// 	}
								// })
							}else{
								uni.hideLoading();
								uni.showToast({
									title:res.message,
									icon:'none'
								})
							}
						}
					})
				}else{
					uni.$u.toast('请阅读并同意用户协议和隐私政策');
				}
				
			},
			ChangeIntegral(e) {
				console.log(e,this.btn)
			},
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					this.http.ajax({
						url:that.http.api.code,
						method:'POST',
						data:{
							mobile:that.form.mobile,
							usage:'register',	
						},
						success(res){ 
							if(res.code==200){
								uni.hideLoading();
								// 这里此提示会被this.start()方法中的提示覆盖
								uni.$u.toast('验证码已发送');
								// 通知验证码组件内部开始倒计时
								that.$refs.uCode.start();
							
							}else{
								uni.hideLoading();
								uni.showToast({
									title:res.message,
									icon:'none'
								})
							}
						}
					})
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			change(e) {
				console.log('change', e);
			},
			goUrl(type) {
				if (type == 1) {
					uni.navigateTo({
						url: '/pages/sign/sign'
					})
				}
			},
			gotoAnli() {

				uni.navigateTo({
					url: '/pages/train/train'
				})
			},


			aa(html) {
				const el = document.createElement('div')
				el.innerHTML = html
				return el.innerText
			},
			gotoDetail(id) {
				uni.navigateTo({
					url: '/pages/main/main?id=' + id
				})
			},
			tabChange(index) {

				this.tabId = index.id
				that.page = 0
				that.list = []
				this.status = 'loadmore'
				this.getlist(index.id)
			}
		}
	}
</script>
<style>
	page,
	body {
		/* background-color: #f3f3f3; */
		background-color: #fff;
		/* height: 100%; */
	}
</style>

<style lang="scss" scoped>
	.page-box {
		position: relative;
		min-height: 100vh;
		overflow: hidden;
	}

	.background-image {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		z-index: -1;
		object-fit: cover;
	}

	.showBtn {
		font-size: 34upx;
		color: #fff;
		background-color: #f4ad32;
		width: 500upx;
		height: 100upx;
		border-radius: 10upx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30upx;
	}

	.signBtn {
		font-size: 34upx;
		color: #fff;
		background-color: #f4ad32;
		width: 570upx;
		height: 74upx;
		border-radius: 100upx;
		display: flex;
		align-items: center;
		box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0,0,0,0.2);
		justify-content: center;
		margin-top: 30upx;
	}
	
</style>